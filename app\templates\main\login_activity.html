{% extends "base.html" %}

{% block title %}Login Activity - O<PERSON> Auth{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-history me-2"></i>Recent Login Activity</h2>
                <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Login Statistics -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="background: #4361ee; color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                    <h4>{{ login_stats.total_logins }}</h4>
                    <p class="mb-0">Total Logins</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="background: #4ade80; color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ login_stats.successful_logins }}</h4>
                    <p class="mb-0">Successful</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="background: #fbbf24; color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h4>{{ login_stats.failed_logins }}</h4>
                    <p class="mb-0">Failed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="background: #4cc9f0; color: white;">
                <div class="card-body text-center">
                    <i class="fas fa-laptop fa-2x mb-2"></i>
                    <h4>{{ login_stats.unique_devices }}</h4>
                    <p class="mb-0">Devices</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Last Login Info -->
    {% if login_stats.last_login %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-clock me-2"></i>
                <strong>Last Login:</strong>
                {{ login_stats.last_login.strftime('%B %d, %Y at %I:%M %p IST') }}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Login History Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Login History
                        <small class="text-muted">(Last 50 logins)</small>
                    </h5>
                </div>
                <div class="card-body">
                    {% if login_history %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead style="background-color: #1e293b; color: white;">
                                <tr>
                                    <th><i class="fas fa-calendar me-1"></i>Date & Time</th>
                                    <th><i class="fas fa-laptop me-1"></i>Device</th>
                                    <th><i class="fas fa-globe me-1"></i>IP Address</th>
                                    <th><i class="fas fa-check-circle me-1"></i>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in login_history %}
                                <tr>
                                    <td>
                                        <div class="fw-bold">
                                            {{ activity.get_ist_login_time().strftime('%b %d, %Y') }}
                                        </div>
                                        <small class="text-muted">
                                            {{ activity.get_ist_login_time().strftime('%I:%M %p IST') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if 'Mobile' in activity.device_info %}
                                            <i class="fas fa-mobile-alt me-2 text-primary"></i>
                                            {% elif 'Tablet' in activity.device_info %}
                                            <i class="fas fa-tablet-alt me-2 text-info"></i>
                                            {% else %}
                                            <i class="fas fa-desktop me-2 text-secondary"></i>
                                            {% endif %}
                                            <div>
                                                <div class="fw-bold">{{ activity.get_device_type() }}</div>
                                                <small class="text-muted">{{ activity.get_browser_name() }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code class="bg-light text-dark p-1 rounded">
                                            {{ activity.ip_address or 'Unknown' }}
                                        </code>
                                    </td>
                                    <td>
                                        {% if activity.success %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Success
                                        </span>
                                        {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Failed
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No login activity found</h5>
                        <p class="text-muted">Your login history will appear here once you start logging in.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Security Notice -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-shield-alt me-2"></i>
                <strong>Security Notice:</strong>
                If you notice any suspicious login activity that you don't recognize,
                please contact support immediately and consider changing your account credentials.
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .table th {
        border-top: none;
        font-weight: 600;
    }

    .badge {
        font-size: 0.75em;
    }

    code {
        font-size: 0.875em;
    }

    .alert {
        border-left: 4px solid;
    }

    .alert-info {
        border-left-color: #0dcaf0;
    }

    .alert-warning {
        border-left-color: #ffc107;
    }
</style>
{% endblock %}