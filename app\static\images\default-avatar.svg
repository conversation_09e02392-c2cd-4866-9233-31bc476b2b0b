<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="100" fill="url(#avatarGradient)" filter="url(#glow)"/>
  
  <!-- User Icon -->
  <g transform="translate(100,100)" fill="white">
    <!-- Head -->
    <circle cx="0" cy="-20" r="25"/>
    
    <!-- Body -->
    <path d="M-35,20 Q-35,0 -25,-10 Q-15,-15 0,-15 Q15,-15 25,-10 Q35,0 35,20 L35,50 Q35,60 25,60 L-25,60 Q-35,60 -35,50 Z"/>
  </g>
  
  <!-- Subtle Border -->
  <circle cx="100" cy="100" r="98" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
</svg>
