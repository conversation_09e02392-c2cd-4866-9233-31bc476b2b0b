{% extends 'base.html' %}

{% block title %}Register - OTP Authentication{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center">
    <div class="col-md-6 col-lg-5 order-md-2">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Register</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('auth.register') }}" id="registerForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" name="username"
                                placeholder="Choose a unique username" maxlength="30" required>
                        </div>
                        <div class="form-text">3-30 characters, letters, numbers, underscores, and dots only</div>
                        <div id="username-feedback" class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="display_name" class="form-label">Display Name (Optional)</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                            <input type="text" class="form-control" id="display_name" name="display_name"
                                placeholder="Your display name" maxlength="50">
                        </div>
                        <div class="form-text">How others will see your name (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone_number" class="form-label">Phone Number</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number"
                                placeholder="+1234567890" required>
                        </div>
                        <div class="form-text">Enter your phone number with country code (e.g., +1 for USA)</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">Send OTP</button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center py-3">
                <p class="mb-0">Already have an account? <a href="{{ url_for('auth.login') }}">Login</a></p>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-lg-5 order-md-1 text-center mb-4 mb-md-0">
        <img src="{{ url_for('static', filename='images/auth-illustration.svg') }}" alt="Register Illustration"
            class="img-fluid illustration-image">
        <h3 class="mt-3 text-primary">Join Us Today!</h3>
        <p class="text-muted">Create your secure account with OTP verification</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const usernameInput = document.getElementById('username');
        const usernameFeedback = document.getElementById('username-feedback');
        const submitBtn = document.getElementById('submitBtn');

        let usernameTimeout;

        usernameInput.addEventListener('input', function () {
            clearTimeout(usernameTimeout);
            const username = this.value.trim().toLowerCase();

            // Clear previous feedback
            this.classList.remove('is-valid', 'is-invalid');
            usernameFeedback.textContent = '';

            if (username.length === 0) return;

            // Basic client-side validation
            if (username.length < 3) {
                showUsernameError('Username must be at least 3 characters');
                return;
            }

            if (username.length > 30) {
                showUsernameError('Username must be less than 30 characters');
                return;
            }

            if (!/^[a-z0-9._]+$/.test(username)) {
                showUsernameError('Username can only contain letters, numbers, underscores, and dots');
                return;
            }

            if (username.startsWith('.') || username.endsWith('.')) {
                showUsernameError('Username cannot start or end with a dot');
                return;
            }

            // Check availability after a delay
            usernameTimeout = setTimeout(() => {
                checkUsernameAvailability(username);
            }, 500);
        });

        function showUsernameError(message) {
            usernameInput.classList.add('is-invalid');
            usernameFeedback.textContent = message;
        }

        function showUsernameSuccess(message) {
            usernameInput.classList.add('is-valid');
            usernameFeedback.textContent = message;
            usernameFeedback.className = 'valid-feedback';
        }

        function checkUsernameAvailability(username) {
            fetch('/auth/check-username', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrf_token]').value
                },
                body: JSON.stringify({ username: username })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.available) {
                        showUsernameSuccess('Username is available! 🎉');
                        if (typeof notifications !== 'undefined') {
                            notifications.success(`Great choice! @${username} is available! ✨`, {
                                title: '🎯 Perfect!',
                                duration: 2000
                            });
                        }
                    } else {
                        showUsernameError('Username is already taken 😔');
                        if (typeof notifications !== 'undefined') {
                            notifications.warning(`Sorry, @${username} is taken. Try another! 💭`, {
                                title: '🤔 Try Again',
                                duration: 3000
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking username:', error);
                    if (typeof notifications !== 'undefined') {
                        notifications.error('Connection error. Please try again! 🔄', {
                            title: '⚠️ Network Issue',
                            duration: 3000
                        });
                    }
                });
        }

        // Welcome notification for register page
        setTimeout(() => {
            if (typeof notifications !== 'undefined') {
                notifications.info('Let\'s create your secure account! Fill in the details below 📝', {
                    title: '🎉 Join Us!',
                    emoji: '🚀',
                    duration: 4000
                });
            }
        }, 500);

        // Add form submission notification
        const form = document.getElementById('registerForm');
        if (form) {
            form.addEventListener('submit', function (e) {
                const username = document.getElementById('username').value.trim();
                const phone = document.getElementById('phone_number').value.trim();

                if (username && phone) {
                    if (typeof notifications !== 'undefined') {
                        notifications.info('Creating your account and sending OTP... 🎯', {
                            title: '🚀 Almost There!',
                            duration: 4000
                        });
                    }
                }
            });
        }
    });
</script>
{% endblock %}