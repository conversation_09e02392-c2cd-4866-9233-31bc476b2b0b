<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}🔐 Premium OTP Authentication{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Premium Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">

    <!-- Premium CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Meta Tags for Premium Experience -->
    <meta name="description"
        content="Experience the future of authentication with our premium OTP verification system. Secure, fast, and beautifully designed.">
    <meta name="theme-color" content="#667eea">
    <meta property="og:title" content="Premium OTP Authentication">
    <meta property="og:description" content="Secure authentication with stunning design">
    <meta property="og:type" content="website">

    {% block extra_css %}{% endblock %}
</head>

<body>
    <!-- Premium Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-shield-alt me-2"></i>Premium Auth
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.about') }}">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown"
                            role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="avatar-sm me-2">
                                <img src="{{ url_for('static', filename='images/default-avatar.svg') }}" alt="Avatar"
                                    class="rounded-circle" style="width: 32px; height: 32px;">
                            </div>
                            {{ current_user.get_display_name() }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-user me-1"></i>{{ current_user.get_display_name() }}
                                    {% if current_user.username %}
                                    <br><small class="text-muted">@{{ current_user.username }}</small>
                                    {% endif %}
                                </h6>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('main.profile') }}">
                                    <i class="fas fa-user me-2"></i>Profile
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('main.login_activity') }}">
                                    <i class="fas fa-history me-2"></i>Login Activity
                                </a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-primary ms-2" href="{{ url_for('auth.register') }}">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Premium Flash Messages -->
    <div class="container" style="margin-top: 100px;">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}
    </div>

    <!-- Premium Main Content -->
    <main class="container py-5">
        {% block content %}{% endblock %}
    </main>

    <!-- Premium Footer -->
    <footer class="mt-5">
        <div class="container text-center py-4">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-gradient mb-3">
                        <i class="fas fa-shield-alt me-2"></i>Premium Auth
                    </h5>
                    <p class="text-white-50">
                        Experience the future of secure authentication with our premium OTP verification system.
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-white mb-3">Quick Links</h6>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ url_for('main.index') }}" class="text-white-50 text-decoration-none">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                        <a href="{{ url_for('main.about') }}" class="text-white-50 text-decoration-none">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                        {% if current_user.is_authenticated %}
                        <a href="{{ url_for('main.dashboard') }}" class="text-white-50 text-decoration-none">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <hr class="my-4" style="border-color: rgba(255, 255, 255, 0.2);">
            <div class="row">
                <div class="col-12">
                    <p class="mb-2 text-white">&copy; 2025 Premium OTP Authentication. All rights reserved.</p>
                    <p class="text-white-50 small mb-0">
                        <i class="fas fa-heart text-danger me-1"></i>
                        Crafted with passion by <strong>Prerak Pithadiya</strong>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>

</html>