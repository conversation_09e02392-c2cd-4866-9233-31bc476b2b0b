from datetime import datetime, timezone, timedelta
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

from app import db, login_manager

# Indian Standard Time (IST) timezone
IST = timezone(timedelta(hours=5, minutes=30))


def get_ist_now():
    """Get current time in Indian Standard Time (IST)."""
    return datetime.now(IST)


def format_ist_datetime(dt):
    """Format datetime to IST if it's timezone-naive, or convert to IST if timezone-aware."""
    if dt is None:
        return None

    # If datetime is timezone-naive, assume it's already in IST
    if dt.tzinfo is None:
        return dt

    # If datetime is timezone-aware, convert to IST
    return dt.astimezone(IST)


class User(UserMixin, db.Model):
    """User model for storing user account information."""

    __tablename__ = "users"

    id = db.Column(db.Integer, primary_key=True)
    phone_number = db.Column(db.String(20), unique=True, nullable=False, index=True)
    username = db.Column(db.String(30), unique=True, nullable=True, index=True)
    display_name = db.Column(db.String(50), nullable=True)
    password_hash = db.Column(db.String(128))
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=get_ist_now)
    updated_at = db.Column(db.DateTime, default=get_ist_now, onupdate=get_ist_now)

    # OTP attempt tracking
    otp_attempts = db.Column(db.Integer, default=0)
    otp_locked_until = db.Column(db.DateTime, nullable=True)

    def __init__(self, phone_number, password=None):
        self.phone_number = phone_number
        if password:
            self.set_password(password)

    def set_password(self, password):
        """Set user password."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check if provided password matches stored hash."""
        return check_password_hash(self.password_hash, password)

    def increment_otp_attempts(self):
        """Increment the number of failed OTP attempts."""
        self.otp_attempts += 1
        db.session.commit()

    def reset_otp_attempts(self):
        """Reset the number of failed OTP attempts."""
        self.otp_attempts = 0
        self.otp_locked_until = None
        db.session.commit()

    def get_display_name(self):
        """Get the best display name for the user."""
        if self.display_name:
            return self.display_name
        elif self.username:
            return self.username
        else:
            return self.phone_number

    def get_username_or_phone(self):
        """Get username if available, otherwise phone number."""
        return self.username if self.username else self.phone_number

    def set_username(self, username):
        """Set username with validation."""
        if username:
            username = username.lower().strip()
            # Basic validation
            if len(username) < 3 or len(username) > 30:
                raise ValueError("Username must be between 3 and 30 characters")
            if not username.replace("_", "").replace(".", "").isalnum():
                raise ValueError(
                    "Username can only contain letters, numbers, underscores, and dots"
                )
            if username.startswith(".") or username.endswith("."):
                raise ValueError("Username cannot start or end with a dot")

            # Check if username already exists
            existing_user = User.query.filter_by(username=username).first()
            if existing_user and existing_user.id != self.id:
                raise ValueError("Username already taken")

            self.username = username
        else:
            self.username = None

    @staticmethod
    def find_by_username_or_phone(identifier):
        """Find user by username or phone number."""
        return User.query.filter(
            (User.username == identifier) | (User.phone_number == identifier)
        ).first()

    def __repr__(self):
        return f"<User {self.get_username_or_phone()}>"


class LoginActivity(db.Model):
    """Model for tracking user login activity."""

    __tablename__ = "login_activities"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    login_time = db.Column(db.DateTime, default=get_ist_now, nullable=False)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    device_info = db.Column(db.String(100), nullable=True)  # Browser/Device type
    location = db.Column(db.String(100), nullable=True)  # City/Country (if available)
    success = db.Column(db.Boolean, default=True, nullable=False)

    # Relationships
    user = db.relationship(
        "User",
        backref=db.backref(
            "login_activities",
            lazy="dynamic",
            order_by="LoginActivity.login_time.desc()",
        ),
    )

    def __repr__(self):
        return f"<LoginActivity {self.login_time} for User {self.user_id}>"

    def get_device_type(self):
        """Extract device type from user agent."""
        if not self.user_agent:
            return "Unknown Device"

        user_agent = self.user_agent.lower()
        if "mobile" in user_agent or "android" in user_agent or "iphone" in user_agent:
            return "Mobile Device"
        elif "tablet" in user_agent or "ipad" in user_agent:
            return "Tablet"
        else:
            return "Desktop/Laptop"

    def get_browser_name(self):
        """Extract browser name from user agent."""
        if not self.user_agent:
            return "Unknown Browser"

        user_agent = self.user_agent.lower()
        if "chrome" in user_agent:
            return "Chrome"
        elif "firefox" in user_agent:
            return "Firefox"
        elif "safari" in user_agent:
            return "Safari"
        elif "edge" in user_agent:
            return "Edge"
        elif "opera" in user_agent:
            return "Opera"
        else:
            return "Other Browser"

    def get_ist_login_time(self):
        """Get login time formatted in IST."""
        return format_ist_datetime(self.login_time)


class OTPLog(db.Model):
    """Model for logging OTP events for audit purposes."""

    __tablename__ = "otp_logs"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    event_type = db.Column(
        db.String(20), nullable=False
    )  # 'generate', 'verify', 'resend'
    status = db.Column(db.String(20), nullable=False)  # 'success', 'failure'
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=get_ist_now)

    # Relationships
    user = db.relationship("User", backref=db.backref("otp_logs", lazy="dynamic"))

    def __repr__(self):
        return f"<OTPLog {self.event_type} {self.status} for User {self.user_id}>"


@login_manager.user_loader
def load_user(user_id):
    """Load user by ID for Flask-Login."""
    return User.query.get(int(user_id))
