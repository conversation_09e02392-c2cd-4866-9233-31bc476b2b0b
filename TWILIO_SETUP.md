# 📱 Twilio SMS Setup Guide

This guide will help you set up <PERSON><PERSON><PERSON> to send real OTP codes to phone numbers.

## 🚀 Quick Setup (5 minutes)

### Step 1: Create Twilio Account
1. Go to [https://www.twilio.com/](https://www.twilio.com/)
2. Click "Sign up for free"
3. Fill in your details and verify your email
4. Complete phone verification with your own phone number

### Step 2: Get Your Credentials
1. After logging in, you'll see the Twilio Console
2. Find your **Account SID** and **Auth Token** on the dashboard
3. Copy these values - you'll need them in Step 4

### Step 3: Get a Phone Number
1. In the Twilio Console, go to **Phone Numbers** → **Manage** → **Buy a number**
2. Choose your country and select a number
3. Click "Buy" (free trial includes $15 credit)
4. Copy the phone number (format: +**********)

### Step 4: Update Your .env File
Replace the placeholder values in your `.env` file:

```env
# Replace these with your actual Twilio credentials
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+**********
```

### Step 5: Test It!
1. Restart your Flask app
2. Try logging in with your verified phone number
3. You should receive a real SMS with the OTP code!

## 📋 Important Notes

### Free Trial Limitations
- **$15 free credit** (enough for ~500 SMS messages)
- Can only send SMS to **verified phone numbers**
- To verify a number: Twilio Console → Phone Numbers → Verified Caller IDs

### Phone Number Format
- Always use international format: `+**********`
- Include country code (e.g., +1 for USA, +44 for UK)

### Troubleshooting
- **"Authentication Error"**: Check your Account SID and Auth Token
- **"Invalid phone number"**: Ensure number is verified in Twilio Console
- **"Insufficient funds"**: Add credit to your Twilio account

## 🔧 Development vs Production

### Development Mode (Current)
- OTP codes shown in console
- No real SMS sent
- Perfect for testing

### Production Mode (With Twilio)
- Real SMS sent to phones
- OTP codes not shown anywhere
- Professional user experience

## 💡 Pro Tips

1. **Test with your own number first** - verify it in Twilio Console
2. **Check the console logs** - they show detailed SMS sending status
3. **Monitor usage** - Twilio Console shows SMS count and costs
4. **International numbers** - may have different costs

## 🆘 Need Help?

If you encounter issues:
1. Check the Flask console for error messages
2. Verify your Twilio credentials in the Twilio Console
3. Ensure your phone number is verified for free trial accounts
4. Check Twilio's status page for service issues

---

**Ready to go live?** Once Twilio is configured, your app will automatically send real SMS messages instead of showing OTP codes on the website! 🎉
