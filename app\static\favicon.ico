<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4e73df;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e59d9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="32" height="32" rx="6" fill="url(#faviconGradient)"/>
  
  <!-- Shield -->
  <g transform="translate(16,16)">
    <path d="M-8,-10 L8,-10 L8,6 L0,12 L-8,6 Z" fill="white"/>
    
    <!-- Lock -->
    <g transform="translate(0,-2)">
      <rect x="-3" y="0" width="6" height="5" rx="1" fill="#4e73df"/>
      <path d="M-2,-3 Q-2,-5 0,-5 Q2,-5 2,-3" stroke="#4e73df" stroke-width="1.5" fill="none"/>
      <circle cx="0" cy="2" r="0.8" fill="white"/>
    </g>
  </g>
</svg>
