# API Documentation

## Authentication Endpoints

### Check Username Availability

**Endpoint:** `POST /auth/check-username`

**Description:** Check if a username is available for registration.

**Request:**
```json
{
    "username": "string (3-30 characters)"
}
```

**Response:**
```json
{
    "available": boolean,
    "message": "string"
}
```

**Example:**
```bash
curl -X POST http://localhost:5000/auth/check-username \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -d '{"username": "johndo<PERSON>"}'
```

**Response Examples:**

Success (Available):
```json
{
    "available": true,
    "message": "Username is available!"
}
```

Error (Taken):
```json
{
    "available": false,
    "message": "Username is already taken"
}
```

Error (Invalid):
```json
{
    "available": false,
    "message": "Username must be between 3 and 30 characters"
}
```

## Development Endpoints

### Show Current OTP (Development Only)

**Endpoint:** `GET /auth/dev-otp/<phone_number>`

**Description:** Display current OTP for a phone number (development mode only).

**Example:**
```bash
curl http://localhost:5000/auth/dev-otp/+**********
```

### Unlock Account (Development Only)

**Endpoint:** `GET|POST /auth/unlock-account`

**Description:** Unlock locked accounts and reset OTP attempts (development mode only).
