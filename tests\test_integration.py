import unittest
from app import create_app, db
from app.models.user import User
from app.services.otp_service import OTPService


class IntegrationTestCase(unittest.TestCase):
    """Integration tests for the complete authentication flow."""

    def setUp(self):
        """Set up test fixtures."""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        
        db.create_all()

    def tearDown(self):
        """Clean up after tests."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    def test_complete_registration_flow(self):
        """Test complete user registration and login flow."""
        # Step 1: Register new user
        response = self.client.post('/auth/register', data={
            'username': 'testuser',
            'display_name': 'Test User',
            'phone_number': '+1234567890'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'verify-otp', response.data)
        
        # Step 2: Verify user was created
        user = User.query.filter_by(phone_number='+1234567890').first()
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'testuser')
        self.assertFalse(user.is_verified)
        
        # Step 3: Simulate OTP verification
        with self.client.session_transaction() as sess:
            sess['otp_user_id'] = user.id
            sess['otp_flow'] = 'registration'
        
        # Mock OTP service to return success
        otp_service = OTPService(self.app.config)
        otp_service.store_otp('+1234567890', '123456')
        
        response = self.client.post('/auth/verify-otp', data={
            'otp': '123456'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'dashboard', response.data)
        
        # Step 4: Verify user is now verified and logged in
        user = User.query.filter_by(phone_number='+1234567890').first()
        self.assertTrue(user.is_verified)

    def test_login_flow_with_username(self):
        """Test login flow using username."""
        # Create verified user
        user = User(phone_number='+1234567890')
        user.set_username('testuser')
        user.is_verified = True
        db.session.add(user)
        db.session.commit()
        
        # Step 1: Login with username
        response = self.client.post('/auth/login', data={
            'identifier': 'testuser'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'verify-otp', response.data)
        
        # Step 2: Verify OTP
        with self.client.session_transaction() as sess:
            sess['otp_user_id'] = user.id
            sess['otp_flow'] = 'login'
        
        otp_service = OTPService(self.app.config)
        otp_service.store_otp('+1234567890', '123456')
        
        response = self.client.post('/auth/verify-otp', data={
            'otp': '123456'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'dashboard', response.data)

    def test_username_availability_api(self):
        """Test username availability API."""
        # Create user with existing username
        user = User(phone_number='+1234567890')
        user.set_username('existing')
        db.session.add(user)
        db.session.commit()
        
        # Test available username
        response = self.client.post('/auth/check-username',
                                  json={'username': 'available'},
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['available'])
        
        # Test taken username
        response = self.client.post('/auth/check-username',
                                  json={'username': 'existing'},
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertFalse(data['available'])

    def test_profile_update(self):
        """Test profile update functionality."""
        # Create and login user
        user = User(phone_number='+1234567890')
        user.is_verified = True
        db.session.add(user)
        db.session.commit()
        
        with self.client.session_transaction() as sess:
            sess['_user_id'] = str(user.id)
            sess['_fresh'] = True
        
        # Update profile
        response = self.client.post('/update-profile', data={
            'username': 'newusername',
            'display_name': 'New Display Name'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Verify updates
        updated_user = User.query.get(user.id)
        self.assertEqual(updated_user.username, 'newusername')
        self.assertEqual(updated_user.display_name, 'New Display Name')


if __name__ == '__main__':
    unittest.main()
