<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="phoneGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e3e6f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4e73df;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e59d9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="#f8f9fc"/>
  
  <!-- Main Phone -->
  <g transform="translate(300,200)">
    <!-- Phone Body -->
    <rect x="-60" y="-120" width="120" height="240" rx="20" fill="url(#phoneGradient)" stroke="#d1d3e2" stroke-width="2"/>
    
    <!-- Screen -->
    <rect x="-50" y="-100" width="100" height="180" rx="10" fill="url(#screenGradient)"/>
    
    <!-- Screen Content -->
    <g fill="white">
      <!-- Header -->
      <rect x="-40" y="-90" width="80" height="20" rx="3" fill="rgba(255,255,255,0.2)"/>
      <text x="0" y="-75" text-anchor="middle" font-family="Arial" font-size="10" fill="white">OTP Verification</text>
      
      <!-- OTP Display -->
      <rect x="-35" y="-50" width="70" height="30" rx="5" fill="white"/>
      <text x="0" y="-30" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#4e73df">123456</text>
      
      <!-- Input Fields -->
      <rect x="-40" y="-10" width="80" height="15" rx="3" fill="rgba(255,255,255,0.3)"/>
      <rect x="-40" y="10" width="80" height="15" rx="3" fill="rgba(255,255,255,0.3)"/>
      
      <!-- Button -->
      <rect x="-30" y="35" width="60" height="20" rx="10" fill="#1cc88a"/>
      <text x="0" y="48" text-anchor="middle" font-family="Arial" font-size="10" fill="white">Verify</text>
    </g>
    
    <!-- Home Button -->
    <circle cx="0" cy="95" r="8" fill="#d1d3e2"/>
  </g>
  
  <!-- Security Icons Around Phone -->
  <g opacity="0.6">
    <!-- Shield Left -->
    <g transform="translate(150,200)">
      <path d="M-15,-20 L15,-20 L15,10 L0,25 L-15,10 Z" fill="#1cc88a"/>
      <path d="M-8,-5 L-3,3 L8,-8" stroke="white" stroke-width="2" fill="none" stroke-linecap="round"/>
    </g>
    
    <!-- Lock Right -->
    <g transform="translate(450,200)">
      <rect x="-10" y="0" width="20" height="15" rx="2" fill="#4e73df"/>
      <path d="M-6,-8 Q-6,-15 0,-15 Q6,-15 6,-8" stroke="#4e73df" stroke-width="3" fill="none"/>
      <circle cx="0" cy="7" r="2" fill="white"/>
    </g>
    
    <!-- Key Top -->
    <g transform="translate(300,80)">
      <circle cx="0" cy="0" r="8" fill="#f6c23e"/>
      <rect x="8" y="-2" width="20" height="4" fill="#f6c23e"/>
      <rect x="25" y="-4" width="3" height="3" fill="#f6c23e"/>
      <rect x="25" y="1" width="3" height="3" fill="#f6c23e"/>
    </g>
    
    <!-- Fingerprint Bottom -->
    <g transform="translate(300,320)">
      <g stroke="#36b9cc" stroke-width="2" fill="none">
        <ellipse cx="0" cy="0" rx="8" ry="12"/>
        <ellipse cx="0" cy="0" rx="5" ry="8"/>
        <ellipse cx="0" cy="0" rx="2" ry="4"/>
      </g>
    </g>
  </g>
  
  <!-- Connecting Lines -->
  <g stroke="#4e73df" stroke-width="1" opacity="0.3" stroke-dasharray="5,5">
    <line x1="165" y1="200" x2="240" y2="200"/>
    <line x1="360" y1="200" x2="435" y2="200"/>
    <line x1="300" y1="95" x2="300" y2="80"/>
    <line x1="300" y1="305" x2="300" y2="320"/>
  </g>
  
  <!-- Floating Particles -->
  <g fill="#4e73df" opacity="0.4">
    <circle cx="100" cy="100" r="2"/>
    <circle cx="500" cy="120" r="1.5"/>
    <circle cx="80" cy="300" r="1"/>
    <circle cx="520" cy="350" r="2"/>
    <circle cx="150" cy="350" r="1.5"/>
    <circle cx="450" cy="80" r="1"/>
  </g>
</svg>
