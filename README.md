# OTP-Based User Authentication Web Application

A secure, user-friendly Python-based web application that employs One-Time Password (OTP) verification for strengthening user authentication.

## Features

- User registration and login with phone number
- Secure OTP generation and verification
- SMS-based OTP delivery
- Session management
- Responsive UI design

## Tech Stack

- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **Backend**: <PERSON>las<PERSON> (Python)
- **Database**: SQLite (development), PostgreSQL (production)
- **OTP Storage**: Redis
- **SMS Gateway**: Twilio

## Setup Instructions

### Prerequisites

- Python 3.8+
- Redis server
- Twilio account (for SMS delivery)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/OTP-Auth-App.git
   cd OTP-Auth-App
   ```

2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   venv\Scripts\activate  # On Windows
   source venv/bin/activate  # On Unix or MacOS
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file in the root directory with the following variables:
   ```
   FLASK_APP=run.py
   FLASK_ENV=development
   SECRET_KEY=your_secret_key
   TWILIO_ACCOUNT_SID=your_twilio_sid
   TWILIO_AUTH_TOKEN=your_twilio_auth_token
   TWILIO_PHONE_NUMBER=your_twilio_phone_number
   REDIS_URL=redis://localhost:6379/0
   ```

5. Initialize the database:
   ```
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

6. Run the application:
   ```
   flask run
   ```

7. Access the application at `http://localhost:5000`

## Project Structure

```
OTP-Auth-App/
├── app/
│   ├── controllers/    # Route handlers
│   ├── models/         # Database models
│   ├── services/       # Business logic and external services
│   ├── static/         # CSS, JS, and other static files
│   │   ├── css/
│   │   └── js/
│   └── templates/      # HTML templates
├── migrations/         # Database migrations
├── .env                # Environment variables (not in version control)
├── .gitignore
├── config.py           # Application configuration
├── README.md
├── requirements.txt    # Project dependencies
└── run.py              # Application entry point
```

## Security Considerations

- OTPs are hashed before storage
- OTPs expire after 5 minutes
- Rate limiting to prevent brute force attacks
- HTTPS enforced in production
- Session management with secure cookies

## License

MIT

## Author

Prerak Pithadiya
