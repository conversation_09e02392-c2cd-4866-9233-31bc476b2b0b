<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="350" viewBox="0 0 500 350" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1cc88a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#17a673;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="500" height="350" fill="#f8f9fc"/>
  
  <!-- Main Dashboard Container -->
  <g transform="translate(250,175)">
    <!-- Dashboard Background -->
    <rect x="-200" y="-150" width="400" height="300" rx="15" fill="url(#cardGradient)" stroke="#e3e6f0" stroke-width="2"/>
    
    <!-- Header -->
    <rect x="-190" y="-140" width="380" height="40" rx="8" fill="#4e73df"/>
    <text x="0" y="-115" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="white">User Dashboard</text>
    
    <!-- User Avatar -->
    <g transform="translate(-150,-60)">
      <circle cx="0" cy="0" r="25" fill="url(#successGradient)"/>
      <text x="0" y="5" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="white">U</text>
    </g>
    
    <!-- User Info -->
    <g transform="translate(-100,-60)">
      <text x="0" y="-10" font-family="Arial" font-size="14" font-weight="bold" fill="#5a5c69">Welcome back!</text>
      <text x="0" y="10" font-family="Arial" font-size="12" fill="#858796">Your account is secure</text>
    </g>
    
    <!-- Status Cards -->
    <g transform="translate(-120,20)">
      <!-- Security Status -->
      <rect x="0" y="0" width="100" height="60" rx="8" fill="url(#successGradient)"/>
      <text x="50" y="20" text-anchor="middle" font-family="Arial" font-size="10" fill="white">Security Status</text>
      <text x="50" y="35" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">SECURE</text>
      <circle cx="20" cy="45" r="3" fill="white"/>
    </g>
    
    <g transform="translate(20,20)">
      <!-- Last Login -->
      <rect x="0" y="0" width="100" height="60" rx="8" fill="#36b9cc"/>
      <text x="50" y="20" text-anchor="middle" font-family="Arial" font-size="10" fill="white">Last Login</text>
      <text x="50" y="35" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Today</text>
      <text x="50" y="50" text-anchor="middle" font-family="Arial" font-size="10" fill="white">12:30 PM</text>
    </g>
    
    <!-- Activity Chart -->
    <g transform="translate(-50,100)">
      <rect x="0" y="0" width="120" height="40" rx="5" fill="white" stroke="#e3e6f0"/>
      <text x="60" y="15" text-anchor="middle" font-family="Arial" font-size="10" fill="#5a5c69">Login Activity</text>
      
      <!-- Simple Bar Chart -->
      <g transform="translate(10,25)">
        <rect x="0" y="0" width="8" height="10" fill="#4e73df"/>
        <rect x="12" y="-3" width="8" height="13" fill="#1cc88a"/>
        <rect x="24" y="2" width="8" height="8" fill="#36b9cc"/>
        <rect x="36" y="-1" width="8" height="11" fill="#f6c23e"/>
        <rect x="48" y="1" width="8" height="9" fill="#e74a3b"/>
        <rect x="60" y="-2" width="8" height="12" fill="#4e73df"/>
        <rect x="72" y="0" width="8" height="10" fill="#1cc88a"/>
        <rect x="84" y="3" width="8" height="7" fill="#36b9cc"/>
        <rect x="96" y="-1" width="8" height="11" fill="#f6c23e"/>
      </g>
    </g>
    
    <!-- Security Icons -->
    <g opacity="0.7">
      <!-- Shield -->
      <g transform="translate(150,-60)">
        <path d="M-8,-12 L8,-12 L8,6 L0,15 L-8,6 Z" fill="#1cc88a"/>
        <path d="M-4,-2 L-1,3 L6,-5" stroke="white" stroke-width="1.5" fill="none" stroke-linecap="round"/>
      </g>
      
      <!-- Lock -->
      <g transform="translate(150,40)">
        <rect x="-6" y="0" width="12" height="10" rx="1" fill="#4e73df"/>
        <path d="M-3,-5 Q-3,-8 0,-8 Q3,-8 3,-5" stroke="#4e73df" stroke-width="2" fill="none"/>
        <circle cx="0" cy="5" r="1" fill="white"/>
      </g>
    </g>
  </g>
  
  <!-- Decorative Elements -->
  <g opacity="0.3">
    <circle cx="50" cy="50" r="3" fill="#4e73df"/>
    <circle cx="450" cy="80" r="2" fill="#1cc88a"/>
    <circle cx="80" cy="300" r="2" fill="#36b9cc"/>
    <circle cx="420" cy="320" r="3" fill="#f6c23e"/>
  </g>
</svg>
