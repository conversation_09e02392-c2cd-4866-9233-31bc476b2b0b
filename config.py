import os
from datetime import timedelta
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Base configuration class."""

    SECRET_KEY = os.environ.get("SECRET_KEY") or "dev-key-for-development-only"
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL") or "sqlite:///app.db"
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Redis configuration for OTP storage
    REDIS_URL = os.environ.get("REDIS_URL") or "redis://localhost:6379/0"

    # Twilio configuration for SMS
    TWILIO_ACCOUNT_SID = os.environ.get("TWILIO_ACCOUNT_SID")
    TWILIO_AUTH_TOKEN = os.environ.get("TWILIO_AUTH_TOKEN")
    TWILIO_PHONE_NUMBER = os.environ.get("TWILIO_PHONE_NUMBER")

    # OTP configuration
    OTP_LENGTH = 6
    OTP_EXPIRY = timedelta(minutes=5)
    OTP_MAX_ATTEMPTS = 3
    OTP_RESEND_LIMIT = 3
    OTP_LOCKOUT_ENABLED = True  # Enable/disable account lockout
    OTP_LOCKOUT_DURATION = timedelta(minutes=30)  # How long to lock accounts

    # Session configuration
    SESSION_TYPE = "filesystem"
    PERMANENT_SESSION_LIFETIME = timedelta(hours=1)

    # Security configuration
    WTF_CSRF_ENABLED = True

    # Logging configuration
    LOG_LEVEL = os.environ.get("LOG_LEVEL") or "INFO"


class DevelopmentConfig(Config):
    """Development configuration."""

    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL") or "sqlite:///dev.db"

    # Development-friendly OTP settings
    OTP_LOCKOUT_ENABLED = False  # Disable lockout in development
    OTP_MAX_ATTEMPTS = 10  # Allow more attempts in development


class TestingConfig(Config):
    """Testing configuration."""

    TESTING = True
    SQLALCHEMY_DATABASE_URI = "sqlite:///test.db"
    WTF_CSRF_ENABLED = False


class ProductionConfig(Config):
    """Production configuration."""

    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL")

    # In production, ensure SECRET_KEY is set in environment
    SECRET_KEY = os.environ.get("SECRET_KEY")

    # Security settings for production
    SESSION_COOKIE_SECURE = True
    REMEMBER_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True


# Configuration dictionary
config = {
    "development": DevelopmentConfig,
    "testing": TestingConfig,
    "production": ProductionConfig,
    "default": DevelopmentConfig,
}
