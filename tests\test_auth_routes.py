import unittest
from unittest.mock import patch, MagicMock
from flask import url_for

from app import create_app, db
from app.models.user import User


class TestAuthRoutes(unittest.TestCase):
    """Test cases for authentication routes."""

    def setUp(self):
        """Set up test fixtures."""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        db.create_all()
        
        # Create test user
        self.test_phone = '+1234567890'
        self.test_user = User(phone_number=self.test_phone)
        db.session.add(self.test_user)
        db.session.commit()

    def tearDown(self):
        """Tear down test fixtures."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()

    @patch('app.controllers.auth.OTPService')
    def test_register_get(self, mock_otp_service):
        """Test GET request to register page."""
        response = self.client.get('/auth/register')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Register', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_register_post_valid(self, mock_otp_service):
        """Test POST request to register with valid data."""
        # Setup mock OTP service
        mock_instance = mock_otp_service.return_value
        mock_instance.generate_otp.return_value = '123456'
        mock_instance.store_otp.return_value = None
        mock_instance.send_otp_sms.return_value = (True, "OTP sent successfully")
        
        # New phone number that doesn't exist yet
        new_phone = '+9876543210'
        
        response = self.client.post('/auth/register', data={
            'phone_number': new_phone
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'OTP sent to your phone number', response.data)
        
        # Verify user was created
        user = User.query.filter_by(phone_number=new_phone).first()
        self.assertIsNotNone(user)

    @patch('app.controllers.auth.OTPService')
    def test_register_post_existing_user(self, mock_otp_service):
        """Test POST request to register with existing phone number."""
        response = self.client.post('/auth/register', data={
            'phone_number': self.test_phone
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'This phone number is already registered', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_login_get(self, mock_otp_service):
        """Test GET request to login page."""
        response = self.client.get('/auth/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Login', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_login_post_valid(self, mock_otp_service):
        """Test POST request to login with valid data."""
        # Setup mock OTP service
        mock_instance = mock_otp_service.return_value
        mock_instance.generate_otp.return_value = '123456'
        mock_instance.store_otp.return_value = None
        mock_instance.send_otp_sms.return_value = (True, "OTP sent successfully")
        
        response = self.client.post('/auth/login', data={
            'phone_number': self.test_phone
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'OTP sent to your phone number', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_login_post_nonexistent_user(self, mock_otp_service):
        """Test POST request to login with non-existent phone number."""
        response = self.client.post('/auth/login', data={
            'phone_number': '+9999999999'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Phone number not registered', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_verify_otp_valid(self, mock_otp_service):
        """Test OTP verification with valid OTP."""
        # Setup mock OTP service
        mock_instance = mock_otp_service.return_value
        mock_instance.verify_otp.return_value = (True, "Verification successful. Welcome!")
        
        # Setup session
        with self.client.session_transaction() as session:
            session['otp_user_id'] = self.test_user.id
            session['otp_flow'] = 'login'
        
        response = self.client.post('/auth/verify-otp', data={
            'otp': '123456'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Verification successful', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_verify_otp_invalid(self, mock_otp_service):
        """Test OTP verification with invalid OTP."""
        # Setup mock OTP service
        mock_instance = mock_otp_service.return_value
        mock_instance.verify_otp.return_value = (False, "Invalid OTP. Please try again.")
        
        # Setup session
        with self.client.session_transaction() as session:
            session['otp_user_id'] = self.test_user.id
            session['otp_flow'] = 'login'
        
        response = self.client.post('/auth/verify-otp', data={
            'otp': '999999'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Invalid OTP', response.data)

    @patch('app.controllers.auth.OTPService')
    def test_resend_otp(self, mock_otp_service):
        """Test OTP resend functionality."""
        # Setup mock OTP service
        mock_instance = mock_otp_service.return_value
        mock_instance.check_resend_limit.return_value = True
        mock_instance.generate_otp.return_value = '123456'
        mock_instance.store_otp.return_value = None
        mock_instance.send_otp_sms.return_value = (True, "OTP sent successfully")
        
        # Setup session
        with self.client.session_transaction() as session:
            session['otp_user_id'] = self.test_user.id
            session['otp_flow'] = 'login'
        
        response = self.client.post('/auth/resend-otp', follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'OTP sent to your phone number', response.data)
        mock_instance.increment_resend_count.assert_called_once()


if __name__ == '__main__':
    unittest.main()
