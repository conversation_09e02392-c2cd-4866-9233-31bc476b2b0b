{% extends 'base.html' %}

{% block title %}Dashboard - OTP Authentication{% endblock %}

{% block content %}
<div class="row align-items-center mb-4">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-body">
                <h2 class="card-title">Welcome, {{ current_user.get_display_name() }}!</h2>
                <p class="card-text">You have successfully authenticated using OTP verification.</p>
                {% if current_user.username %}
                <p class="text-muted mb-0">
                    <i class="fas fa-user me-1"></i>@{{ current_user.username }}
                </p>
                {% else %}
                <div class="alert alert-warning py-2 px-3 mb-0 d-inline-block" style="font-size: 0.9rem;">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <strong>Complete your profile:</strong>
                    <a href="{{ url_for('main.profile') }}" class="alert-link">Add a username</a> to enhance your
                    experience!
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-lg-4 text-center">
        <img src="{{ url_for('static', filename='images/dashboard-hero.svg') }}" alt="Dashboard"
            class="img-fluid illustration-image">
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h4>Account Security</h4>
                <p>Your account is protected with OTP verification.</p>
                <a href="{{ url_for('main.profile') }}" class="btn btn-outline-primary">Manage Security</a>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">
                    <i class="fas fa-history"></i>
                </div>
                <h4>Login Activity</h4>
                <p>View your recent login activities.</p>
                <a href="{{ url_for('main.login_activity') }}" class="btn btn-outline-primary">View Activity</a>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">
                    <i class="fas fa-cog"></i>
                </div>
                <h4>Settings</h4>
                <p>Manage your account preferences.</p>
                <button class="btn btn-outline-primary" disabled>Coming Soon</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">Security Tips</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Never Share Your OTP</h6>
                            <p class="text-muted mb-0">Keep your one-time passwords confidential. Legitimate services
                                will never ask for your OTP over phone or email.</p>
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Verify Sender</h6>
                            <p class="text-muted mb-0">Always check that OTP messages come from our official number.</p>
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Report Suspicious Activity</h6>
                            <p class="text-muted mb-0">If you receive an OTP without requesting one, contact support
                                immediately.</p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Add click notifications for dashboard cards
        const securityCard = document.querySelector('a[href*="profile"]');
        const activityCard = document.querySelector('a[href*="login_activity"]');

        if (securityCard) {
            securityCard.addEventListener('click', function (e) {
                if (typeof notifications !== 'undefined') {
                    notifications.info('Opening your security settings... 🔐', {
                        title: '🛡️ Security',
                        duration: 2000
                    });
                }
            });
        }

        if (activityCard) {
            activityCard.addEventListener('click', function (e) {
                if (typeof notifications !== 'undefined') {
                    notifications.info('Loading your login history... 📊', {
                        title: '📈 Activity',
                        duration: 2000
                    });
                }
            });
        }

        // Add hover effects for security tips
        const securityTips = document.querySelectorAll('.list-group-item');
        securityTips.forEach((tip, index) => {
            tip.addEventListener('mouseenter', function () {
                this.style.transform = 'translateX(10px)';
                this.style.transition = 'all 0.3s ease';
                this.style.backgroundColor = '#f8f9fc';
            });

            tip.addEventListener('mouseleave', function () {
                this.style.transform = 'translateX(0)';
                this.style.backgroundColor = '';
            });
        });

        // Add celebration for successful login
        {% if current_user.is_authenticated %}
        setTimeout(() => {
            if (typeof createConfetti !== 'undefined') {
                createConfetti();
            }
        }, 1000);
        {% endif %}
    });
</script>
{% endblock %}