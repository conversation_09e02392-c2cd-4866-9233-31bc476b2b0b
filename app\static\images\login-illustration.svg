<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="formGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="#f8f9fc"/>
  
  <!-- Main Login Form -->
  <g transform="translate(200,150)">
    <!-- Form Container -->
    <rect x="-120" y="-100" width="240" height="200" rx="15" fill="url(#formGradient)" stroke="#e3e6f0" stroke-width="2"/>
    
    <!-- Header -->
    <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#4e73df">Login</text>
    
    <!-- Phone Icon -->
    <g transform="translate(0,-40)">
      <rect x="-8" y="-12" width="16" height="24" rx="3" fill="#4e73df"/>
      <rect x="-6" y="-8" width="12" height="16" rx="1" fill="white"/>
      <circle cx="0" cy="8" r="2" fill="#4e73df"/>
    </g>
    
    <!-- Input Fields -->
    <rect x="-80" y="-10" width="160" height="25" rx="5" fill="white" stroke="#d1d3e2"/>
    <text x="-75" y="8" font-family="Arial" font-size="12" fill="#858796">Phone Number</text>
    
    <!-- OTP Input -->
    <rect x="-80" y="25" width="160" height="25" rx="5" fill="white" stroke="#d1d3e2"/>
    <text x="-75" y="43" font-family="Arial" font-size="12" fill="#858796">Enter OTP</text>
    
    <!-- Login Button -->
    <rect x="-60" y="65" width="120" height="30" rx="15" fill="#4e73df"/>
    <text x="0" y="85" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">Login</text>
  </g>
  
  <!-- Security Elements -->
  <g opacity="0.6">
    <!-- Lock Left -->
    <g transform="translate(80,150)">
      <rect x="-8" y="0" width="16" height="12" rx="2" fill="#1cc88a"/>
      <path d="M-5,-6 Q-5,-10 0,-10 Q5,-10 5,-6" stroke="#1cc88a" stroke-width="2" fill="none"/>
      <circle cx="0" cy="6" r="1.5" fill="white"/>
    </g>
    
    <!-- Shield Right -->
    <g transform="translate(320,150)">
      <path d="M-10,-15 L10,-15 L10,8 L0,20 L-10,8 Z" fill="#36b9cc"/>
      <path d="M-5,0 L-1,6 L8,-6" stroke="white" stroke-width="2" fill="none" stroke-linecap="round"/>
    </g>
    
    <!-- Key Top -->
    <g transform="translate(200,50)">
      <circle cx="0" cy="0" r="6" fill="#f6c23e"/>
      <rect x="6" y="-1.5" width="15" height="3" fill="#f6c23e"/>
      <rect x="18" y="-3" width="3" height="2" fill="#f6c23e"/>
      <rect x="18" y="1" width="3" height="2" fill="#f6c23e"/>
    </g>
  </g>
  
  <!-- Connecting Lines -->
  <g stroke="#4e73df" stroke-width="1" opacity="0.3" stroke-dasharray="3,3">
    <line x1="95" y1="150" x2="140" y2="150"/>
    <line x1="260" y1="150" x2="305" y2="150"/>
    <line x1="200" y1="65" x2="200" y2="50"/>
  </g>
  
  <!-- Floating Elements -->
  <g fill="#4e73df" opacity="0.3">
    <circle cx="50" cy="80" r="1.5"/>
    <circle cx="350" cy="100" r="1"/>
    <circle cx="60" cy="220" r="1"/>
    <circle cx="340" cy="250" r="1.5"/>
  </g>
</svg>
