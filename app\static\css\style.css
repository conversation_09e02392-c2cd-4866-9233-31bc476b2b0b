/* 🚀 PREMIUM OTP AUTHENTICATION - MODERN DESIGN SYSTEM 🚀 */

/* ===== DESIGN TOKENS ===== */
:root {
  /* 🎨 Brand Gradients */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --hero-gradient: linear-gradient(
    135deg,
    #667eea 0%,
    #764ba2 50%,
    #f093fb 100%
  );

  /* ✨ Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(20px);

  /* 🌙 Dark Theme */
  --dark-bg: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  --dark-surface: rgba(255, 255, 255, 0.05);
  --dark-border: rgba(255, 255, 255, 0.1);
  --dark-text: #e2e8f0;

  /* 📝 Typography */
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-display: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;

  /* 📐 Spacing & Sizing */
  --border-radius: 20px;
  --border-radius-lg: 28px;
  --border-radius-xl: 36px;
  --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 30px 80px rgba(0, 0, 0, 0.2);

  /* ⚡ Animations */
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== GLOBAL FOUNDATION ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background: var(--hero-gradient);
  background-attachment: fixed;
  color: #2d3748;
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* 🌟 Animated Background Particles */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(120, 119, 198, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 119, 198, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(120, 219, 255, 0.3) 0%,
      transparent 50%
    );
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(30px) rotate(240deg);
  }
}

/* 🎯 Selection Styling */
::selection {
  background: var(--primary-gradient);
  color: white;
}

/* 📱 Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-gradient);
}

/* ===== NAVIGATION ===== */
.navbar {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-smooth);
}

.navbar-brand {
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-brand i {
  margin-right: 0.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-link {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9) !important;
  transition: all var(--transition-smooth);
  position: relative;
}

.nav-link:hover {
  color: white !important;
  transform: translateY(-2px);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: all var(--transition-smooth);
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 100%;
}

/* ===== GLASSMORPHISM CARDS ===== */
.card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-smooth);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
}

.card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-strong);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid var(--glass-border);
  border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
  font-weight: 600;
  color: white;
}

.card-body {
  color: rgba(255, 255, 255, 0.9);
}

.card-title {
  font-family: var(--font-display);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

/* ===== PREMIUM FORMS ===== */
.form-control {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  color: white;
  font-weight: 500;
  padding: 0.875rem 1.25rem;
  transition: all var(--transition-smooth);
  position: relative;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  transform: scale(1.02);
  color: white;
}

.form-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.form-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

/* ===== STUNNING BUTTONS ===== */
.btn {
  font-weight: 600;
  padding: 0.875rem 2rem;
  border-radius: var(--border-radius);
  border: none;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
  background: var(--primary-gradient);
  filter: brightness(1.1);
}

.btn-outline-primary {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: var(--glass-backdrop);
}

.btn-outline-primary:hover {
  background: var(--primary-gradient);
  border-color: transparent;
  color: white;
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

.btn-secondary {
  background: var(--secondary-gradient);
  color: white;
}

/* ===== PREMIUM OTP INPUTS ===== */
.otp-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  perspective: 1000px;
}

.otp-digit {
  width: 60px;
  height: 70px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  border-radius: var(--border-radius);
  text-align: center;
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.otp-digit::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
}

.otp-digit:focus {
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  transform: scale(1.1) rotateY(10deg);
  background: rgba(255, 255, 255, 0.2);
}

.otp-digit.filled {
  background: var(--primary-gradient);
  border-color: rgba(255, 255, 255, 0.8);
  animation: fillSuccess 0.4s var(--bounce);
  transform: scale(1.05);
}

@keyframes fillSuccess {
  0% {
    transform: scale(1) rotateY(0deg);
  }
  50% {
    transform: scale(1.2) rotateY(180deg);
  }
  100% {
    transform: scale(1.05) rotateY(360deg);
  }
}

/* Legacy OTP Input */
#otp {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  border-radius: var(--border-radius);
  letter-spacing: 0.75rem;
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  text-align: center;
  padding: 1rem;
  max-width: 300px;
}

/* ===== FEATURE ICONS ===== */
.feature-icon {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  color: white;
  font-size: 1.5rem;
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.feature-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
}

.feature-icon:hover {
  transform: scale(1.1) rotate(5deg);
  background: var(--primary-gradient);
  box-shadow: var(--shadow-medium);
}

/* ===== PREMIUM DASHBOARD ===== */
.dashboard-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-gradient);
}

.dashboard-card:hover {
  transform: translateY(-15px) scale(1.03);
  box-shadow: var(--shadow-strong);
  border-color: rgba(255, 255, 255, 0.4);
}

.dashboard-card .card-body {
  padding: 2rem;
}

.dashboard-card h5 {
  font-family: var(--font-display);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.dashboard-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* ===== PROFILE STYLES ===== */
.avatar {
  margin: 2rem 0;
  position: relative;
}

.avatar img {
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-medium);
  transition: all var(--transition-smooth);
}

.avatar:hover img {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: var(--shadow-strong);
}

/* ===== FOOTER ===== */
footer {
  margin-top: auto;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-top: 1px solid var(--glass-border);
  color: rgba(255, 255, 255, 0.8);
  padding: 2rem 0;
}

/* ===== PREMIUM IMAGES ===== */
.img-fluid {
  max-width: 100%;
  height: auto;
  transition: all var(--transition-smooth);
}

.hero-image {
  max-height: 500px;
  object-fit: contain;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  animation: float-gentle 6s ease-in-out infinite;
}

.illustration-image {
  max-height: 350px;
  object-fit: contain;
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.2));
  transition: all var(--transition-smooth);
}

.illustration-image:hover {
  transform: scale(1.05) rotate(2deg);
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.3));
}

.avatar-image {
  object-fit: cover;
  transition: all var(--transition-smooth);
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

/* ===== LOADING STATES ===== */
img {
  transition: all var(--transition-smooth);
  opacity: 1;
}

img:not([src]) {
  opacity: 0;
  transform: scale(0.9);
}

img[src] {
  animation: imageLoad 0.5s var(--bounce);
}

@keyframes imageLoad {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* ===== PREMIUM NOTIFICATIONS ===== */
.toast-container {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 9999;
  max-width: 400px;
}

.modern-toast {
  min-width: 380px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  animation: slideInRight 0.6s var(--bounce);
  position: relative;
  overflow: hidden;
}

.modern-toast::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

.modern-toast.success::before {
  background: var(--success-gradient);
}

.modern-toast.error::before {
  background: var(--danger-gradient);
}

.modern-toast.warning::before {
  background: var(--warning-gradient);
}

.modern-toast.info::before {
  background: var(--primary-gradient);
}

.toast-header {
  background: transparent;
  border-bottom: 1px solid var(--glass-border);
  color: white;
  font-weight: 700;
  font-family: var(--font-display);
  padding: 1rem 1.5rem 0.5rem;
}

.toast-body {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  line-height: 1.6;
  padding: 0.5rem 1.5rem 1.5rem;
}

.toast-emoji {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  animation: bounce 2s infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* ===== PREMIUM ANIMATIONS ===== */
@keyframes slideInRight {
  0% {
    transform: translateX(120%) scale(0.8);
    opacity: 0;
  }
  60% {
    transform: translateX(-10%) scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-15px) scale(1.1);
  }
  60% {
    transform: translateY(-8px) scale(1.05);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.pulse-animation {
  animation: pulse 3s infinite;
}

/* ===== PREMIUM PROGRESS BARS ===== */
.progress-modern {
  height: 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.progress-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
}

.progress-modern .progress-bar {
  background: var(--success-gradient);
  border-radius: 10px;
  transition: width 0.6s var(--bounce);
  position: relative;
  overflow: hidden;
}

.progress-modern .progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ===== PREMIUM ALERTS ===== */
.alert {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  color: rgba(255, 255, 255, 0.9);
  border-left: 4px solid;
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
}

.alert-success {
  border-left-color: #4facfe;
}

.alert-danger {
  border-left-color: #fa709a;
}

.alert-warning {
  border-left-color: #43e97b;
}

.alert-info {
  border-left-color: #667eea;
}

/* ===== PREMIUM BADGES ===== */
.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger-gradient);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
  box-shadow: var(--shadow-soft);
}

/* ===== PREMIUM LOADING STATES ===== */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.loading-dots::after {
  content: "";
  display: inline-flex;
  gap: 0.25rem;
  animation: dots 1.8s infinite;
}

@keyframes dots {
  0%,
  20% {
    content: "●";
    opacity: 0.4;
  }
  40% {
    content: "● ●";
    opacity: 0.7;
  }
  60% {
    content: "● ● ●";
    opacity: 1;
  }
  80%,
  100% {
    content: "● ● ● ●";
    opacity: 0.4;
  }
}

/* ===== PREMIUM CONFETTI ===== */
.confetti {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10000;
}

.confetti-piece {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--primary-gradient);
  border-radius: 50%;
  animation: confetti-fall 4s linear infinite;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg) scale(0);
    opacity: 0;
  }
}

/* ===== PREMIUM RIPPLE EFFECTS ===== */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.8s ease-out;
  pointer-events: none;
}

@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== PREMIUM TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-display);
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.display-1,
.display-2,
.display-3,
.display-4 {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* ===== PREMIUM LIST STYLES ===== */
.list-group-item {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  color: rgba(255, 255, 255, 0.9);
  transition: all var(--transition-smooth);
  margin-bottom: 0.5rem;
  border-radius: var(--border-radius);
}

.list-group-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(10px);
  border-color: rgba(255, 255, 255, 0.3);
}

/* ===== PREMIUM TYPING INDICATOR ===== */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-radius: var(--border-radius);
  border: 1px solid var(--glass-border);
}

.typing-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--primary-gradient);
  animation: typing 1.6s infinite ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ===== PREMIUM SUCCESS ANIMATIONS ===== */
.checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #4facfe;
  stroke-miterlimit: 10;
  margin: 2rem auto;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  animation: checkmark-appear 0.6s var(--bounce) forwards;
}

@keyframes checkmark-appear {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* ===== PREMIUM RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .hero-image {
    max-height: 400px;
  }

  .illustration-image {
    max-height: 300px;
  }
}

@media (max-width: 992px) {
  .navbar-brand {
    font-size: 1.25rem;
  }

  .card {
    margin-bottom: 1.5rem;
  }

  .dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
  }
}

@media (max-width: 768px) {
  /* Mobile Navigation */
  .navbar {
    padding: 0.75rem 1rem;
  }

  /* Mobile Cards */
  .card {
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
  }

  .card:hover {
    transform: translateY(-5px) scale(1.01);
  }

  /* Mobile Forms */
  .form-control {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  /* Mobile OTP */
  .otp-container {
    gap: 0.5rem;
    margin: 1.5rem 0;
  }

  .otp-digit {
    width: 45px;
    height: 55px;
    font-size: 1.5rem;
  }

  /* Mobile Notifications */
  .toast-container {
    top: 20px;
    right: 15px;
    left: 15px;
    max-width: none;
  }

  .modern-toast {
    min-width: auto;
    width: 100%;
  }

  /* Mobile Images */
  .hero-image {
    max-height: 250px;
  }

  .illustration-image {
    max-height: 200px;
  }

  /* Mobile Typography */
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.75rem;
  }
  h3 {
    font-size: 1.5rem;
  }

  /* Mobile Dashboard */
  .dashboard-card .card-body {
    padding: 1.5rem;
  }

  /* Mobile Feature Icons */
  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.25rem;
  }
}

@media (max-width: 576px) {
  /* Extra Small Mobile */
  .container-fluid {
    padding: 0 1rem;
  }

  .otp-digit {
    width: 40px;
    height: 50px;
    font-size: 1.25rem;
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }

  .card-body {
    padding: 1.25rem;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }
}

/* ===== PREMIUM ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  body::before {
    animation: none;
  }

  .hero-image {
    animation: none;
  }
}

/* ===== PREMIUM FOCUS STATES ===== */
.btn:focus,
.form-control:focus,
.otp-digit:focus {
  outline: 3px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* ===== PREMIUM PRINT STYLES ===== */
@media print {
  body::before,
  .confetti,
  .toast-container {
    display: none !important;
  }

  .card {
    border: 1px solid #000 !important;
    background: white !important;
    color: black !important;
  }
}
