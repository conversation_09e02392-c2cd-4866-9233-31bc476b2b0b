{% extends 'base.html' %}

{% block title %}About - OTP Authentication{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card shadow-sm">
            <div class="card-body p-5">
                <h1 class="text-center text-primary mb-4">About OTP Authentication</h1>

                <div class="row mb-5">
                    <div class="col-md-6">
                        <img src="{{ url_for('static', filename='images/auth-illustration.svg') }}" alt="Security"
                            class="img-fluid illustration-image rounded shadow">
                    </div>
                    <div class="col-md-6 d-flex align-items-center">
                        <div>
                            <h3>Our Mission</h3>
                            <p>We are dedicated to providing secure, user-friendly authentication solutions that protect
                                user accounts and sensitive information from unauthorized access.</p>
                            <p>Our OTP-based authentication system offers an additional layer of security beyond
                                traditional password-based authentication.</p>
                        </div>
                    </div>
                </div>

                <h3 class="mb-4">Key Features</h3>

                <div class="row mb-5">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-shield-alt fa-3x text-primary"></i>
                                </div>
                                <h4>Enhanced Security</h4>
                                <p>Two-factor authentication with OTP verification adds an extra layer of protection to
                                    your account.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                                </div>
                                <h4>SMS Delivery</h4>
                                <p>Receive secure verification codes directly to your registered phone number.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-clock fa-3x text-primary"></i>
                                </div>
                                <h4>Time-Limited Codes</h4>
                                <p>OTPs expire after 5 minutes, reducing the risk of unauthorized access.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <h3 class="mb-4">How It Works</h3>

                <div class="timeline mb-5">
                    <div class="row g-0">
                        <div class="col-md-6">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary text-white">1</div>
                                <div class="timeline-content">
                                    <h5>Registration</h5>
                                    <p>Users register with their phone number, which is verified through an OTP sent via
                                        SMS.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-0">
                        <div class="col-md-6 offset-md-6">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary text-white">2</div>
                                <div class="timeline-content">
                                    <h5>Login Request</h5>
                                    <p>When a user attempts to log in, they enter their phone number to request an OTP.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-0">
                        <div class="col-md-6">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary text-white">3</div>
                                <div class="timeline-content">
                                    <h5>OTP Generation & Delivery</h5>
                                    <p>A secure 6-digit OTP is generated and sent to the user's registered phone number.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-0">
                        <div class="col-md-6 offset-md-6">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary text-white">4</div>
                                <div class="timeline-content">
                                    <h5>Verification</h5>
                                    <p>The user enters the received OTP, which is verified by the system.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-0">
                        <div class="col-md-6">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary text-white">5</div>
                                <div class="timeline-content">
                                    <h5>Access Granted</h5>
                                    <p>Upon successful verification, the user is granted access to their account.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h3 class="mb-4">Security Measures</h3>

                <ul class="list-group list-group-flush mb-5">
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Secure OTP Storage</h6>
                            <p class="text-muted mb-0">OTPs are hashed before storage and are never stored in plain
                                text.</p>
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Limited Validity</h6>
                            <p class="text-muted mb-0">OTPs expire after 5 minutes to minimize the risk of interception.
                            </p>
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Rate Limiting</h6>
                            <p class="text-muted mb-0">Limits on OTP requests and verification attempts to prevent brute
                                force attacks.</p>
                        </div>
                    </li>
                    <li class="list-group-item d-flex">
                        <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                        <div>
                            <h6>Audit Logging</h6>
                            <p class="text-muted mb-0">All OTP events are logged for security monitoring and compliance.
                            </p>
                        </div>
                    </li>
                </ul>

                <div class="text-center">
                    <h3 class="mb-4">Ready to Get Started?</h3>
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary btn-lg">Go to Dashboard</a>
                    {% else %}
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg me-2">Register Now</a>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-lg">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding: 20px 0;
    }

    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        width: 2px;
        background-color: #e9ecef;
        transform: translateX(-50%);
    }

    .timeline-item {
        position: relative;
        padding: 20px 30px;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        top: 20px;
        left: -15px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        font-weight: bold;
    }

    .col-md-6.offset-md-6 .timeline-marker {
        left: auto;
        right: -15px;
    }

    .timeline-content {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    @media (max-width: 767px) {
        .timeline:before {
            left: 15px;
        }

        .col-md-6.offset-md-6 {
            margin-left: 0;
        }

        .timeline-marker {
            left: 0;
        }

        .col-md-6.offset-md-6 .timeline-marker {
            left: 0;
            right: auto;
        }

        .timeline-content {
            margin-left: 30px;
        }
    }
</style>
{% endblock %}