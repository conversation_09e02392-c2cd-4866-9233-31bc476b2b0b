# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your_secret_key_here_minimum_32_characters_long

# Database Configuration
# For SQLite (development)
DATABASE_URL=sqlite:///app.db
# For PostgreSQL (production)
# DATABASE_URL=postgresql://username:password@localhost:5432/otpauth
# For MySQL (production)
# DATABASE_URL=mysql://username:password@localhost:3306/otpauth

# Redis Configuration (for OTP storage)
REDIS_URL=redis://localhost:6379/0
# For Redis with password:
# REDIS_URL=redis://:password@localhost:6379/0

# Twilio Configuration (for SMS OTP delivery)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Security Configuration
# OTP_LENGTH=6
# OTP_EXPIRY_MINUTES=5
# OTP_MAX_ATTEMPTS=3
# OTP_RESEND_LIMIT=3

# Logging Configuration
LOG_LEVEL=INFO

# Production Security (uncomment for production)
# SESSION_COOKIE_SECURE=True
# REMEMBER_COOKIE_SECURE=True
# SESSION_COOKIE_HTTPONLY=True
