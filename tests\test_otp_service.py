import unittest
import hashlib
from unittest.mock import patch, MagicMock
from datetime import timedelta
import time

from app.services.otp_service import OTPService


class TestOTPService(unittest.TestCase):
    """Test cases for the OTP Service."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_config = {
            'OTP_LENGTH': 6,
            'OTP_EXPIRY': timedelta(minutes=5),
            'OTP_MAX_ATTEMPTS': 3,
            'OTP_RESEND_LIMIT': 3,
            'TWILIO_ACCOUNT_SID': 'test_sid',
            'TWILIO_AUTH_TOKEN': 'test_token',
            'TWILIO_PHONE_NUMBER': '+**********'
        }
        self.otp_service = OTPService(self.mock_config)
        self.test_phone = '+**********'

    def test_generate_otp(self):
        """Test OTP generation."""
        otp = self.otp_service.generate_otp()
        self.assertEqual(len(otp), 6)
        self.assertTrue(otp.isdigit())

    @patch('app.services.otp_service.redis_client')
    def test_store_otp(self, mock_redis):
        """Test OTP storage in Redis."""
        otp = '123456'
        self.otp_service.store_otp(self.test_phone, otp)
        
        # Check that Redis hset was called with correct parameters
        otp_hash = hashlib.sha256(otp.encode()).hexdigest()
        mock_redis.hset.assert_any_call(f"otp:{self.test_phone}", "hash", otp_hash)
        mock_redis.expire.assert_called_once()

    @patch('app.services.otp_service.redis_client')
    def test_verify_otp_success(self, mock_redis):
        """Test successful OTP verification."""
        otp = '123456'
        otp_hash = hashlib.sha256(otp.encode()).hexdigest()
        
        # Mock Redis response
        mock_redis.hgetall.return_value = {
            "hash": otp_hash,
            "created_at": str(int(time.time()))
        }
        
        result, message = self.otp_service.verify_otp(self.test_phone, otp)
        self.assertTrue(result)
        self.assertEqual(message, "Verification successful. Welcome!")
        mock_redis.delete.assert_called_once()

    @patch('app.services.otp_service.redis_client')
    def test_verify_otp_failure(self, mock_redis):
        """Test failed OTP verification."""
        correct_otp = '123456'
        wrong_otp = '654321'
        otp_hash = hashlib.sha256(correct_otp.encode()).hexdigest()
        
        # Mock Redis response
        mock_redis.hgetall.return_value = {
            "hash": otp_hash,
            "created_at": str(int(time.time()))
        }
        
        result, message = self.otp_service.verify_otp(self.test_phone, wrong_otp)
        self.assertFalse(result)
        self.assertEqual(message, "Invalid OTP. Please try again.")
        mock_redis.delete.assert_not_called()

    @patch('app.services.otp_service.redis_client')
    def test_verify_otp_expired(self, mock_redis):
        """Test expired OTP verification."""
        otp = '123456'
        otp_hash = hashlib.sha256(otp.encode()).hexdigest()
        
        # Mock Redis response with expired timestamp (10 minutes ago)
        expired_time = int(time.time()) - 600
        mock_redis.hgetall.return_value = {
            "hash": otp_hash,
            "created_at": str(expired_time)
        }
        
        result, message = self.otp_service.verify_otp(self.test_phone, otp)
        self.assertFalse(result)
        self.assertEqual(message, "OTP expired. Please request a new code.")
        mock_redis.delete.assert_called_once()

    @patch('app.services.otp_service.Client')
    def test_send_otp_sms(self, mock_twilio_client):
        """Test sending OTP via SMS."""
        # Setup mock Twilio client
        mock_messages = MagicMock()
        mock_twilio_client.return_value.messages = mock_messages
        mock_messages.create.return_value = MagicMock(sid='SM123')
        
        otp = '123456'
        success, message = self.otp_service.send_otp_sms(self.test_phone, otp)
        
        self.assertTrue(success)
        self.assertEqual(message, "OTP sent successfully")
        mock_messages.create.assert_called_once_with(
            body=f"Your verification code is: {otp}. Valid for 5 minutes.",
            from_=self.mock_config['TWILIO_PHONE_NUMBER'],
            to=self.test_phone
        )

    @patch('app.services.otp_service.redis_client')
    def test_check_resend_limit_not_exceeded(self, mock_redis):
        """Test resend limit not exceeded."""
        mock_redis.get.return_value = b'2'  # 2 resends so far
        
        result = self.otp_service.check_resend_limit(self.test_phone)
        self.assertTrue(result)

    @patch('app.services.otp_service.redis_client')
    def test_check_resend_limit_exceeded(self, mock_redis):
        """Test resend limit exceeded."""
        mock_redis.get.return_value = b'3'  # 3 resends (limit reached)
        
        result = self.otp_service.check_resend_limit(self.test_phone)
        self.assertFalse(result)

    @patch('app.services.otp_service.redis_client')
    def test_increment_resend_count_new(self, mock_redis):
        """Test incrementing resend count for new phone number."""
        mock_redis.exists.return_value = False
        
        self.otp_service.increment_resend_count(self.test_phone)
        
        mock_redis.set.assert_called_once_with(f"resend:{self.test_phone}", 1)
        mock_redis.expire.assert_called_once()

    @patch('app.services.otp_service.redis_client')
    def test_increment_resend_count_existing(self, mock_redis):
        """Test incrementing resend count for existing phone number."""
        mock_redis.exists.return_value = True
        
        self.otp_service.increment_resend_count(self.test_phone)
        
        mock_redis.incr.assert_called_once_with(f"resend:{self.test_phone}")


if __name__ == '__main__':
    unittest.main()
