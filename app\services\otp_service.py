import logging
import os
import random
import hashlib
import time
from datetime import datetime, timedelta

import pyotp
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

from app.models.user import OTPLog
from app import db

logger = logging.getLogger(__name__)


class OTPService:
    """Service for OTP generation, verification, and delivery."""

    def __init__(self, app_config):
        """Initialize the OTP service with application configuration."""
        self.otp_length = app_config.get("OTP_LENGTH", 6)
        self.otp_expiry = app_config.get("OTP_EXPIRY", timedelta(minutes=5))
        self.max_attempts = app_config.get("OTP_MAX_ATTEMPTS", 3)
        self.resend_limit = app_config.get("OTP_RESEND_LIMIT", 3)

        # Twilio configuration
        self.twilio_client = None
        self.twilio_phone = app_config.get("TWILIO_PHONE_NUMBER")

        # Initialize Twilio client if credentials are available
        twilio_sid = app_config.get("TWILIO_ACCOUNT_SID")
        twilio_token = app_config.get("TWILIO_AUTH_TOKEN")

        if twilio_sid and twilio_token:
            self.twilio_client = Client(twilio_sid, twilio_token)

    def _get_redis_client(self):
        """Get the Redis client from the app context."""
        from app import redis_client

        return redis_client

    def generate_otp(self):
        """Generate a random OTP of specified length."""
        if self.otp_length == 6:
            # Use pyotp for standard 6-digit TOTP
            totp = pyotp.TOTP(pyotp.random_base32())
            return totp.now()
        else:
            # Generate custom length OTP
            digits = "**********"
            return "".join(random.choice(digits) for _ in range(self.otp_length))

    def _get_otp_key(self, phone_number):
        """Generate a Redis key for storing OTP."""
        return f"otp:{phone_number}"

    def _get_resend_key(self, phone_number):
        """Generate a Redis key for tracking resend count."""
        return f"resend:{phone_number}"

    def store_otp(self, phone_number, otp):
        """Store OTP with hash in Redis with expiration."""
        # Hash the OTP for secure storage
        otp_hash = hashlib.sha256(otp.encode()).hexdigest()

        # Store in Redis with expiration
        key = self._get_otp_key(phone_number)
        expiry_seconds = int(self.otp_expiry.total_seconds())
        current_time = int(time.time())

        # Store as hash with creation timestamp
        redis_client = self._get_redis_client()
        redis_client.hset(key, "hash", otp_hash)
        redis_client.hset(key, "created_at", current_time)
        redis_client.expire(key, expiry_seconds)

        logger.info(
            f"OTP stored for {phone_number} with expiry of {expiry_seconds} seconds"
        )

    def verify_otp(self, phone_number, otp):
        """Verify the provided OTP against stored hash with thorough validation."""
        # Input validation
        if not otp:
            logger.warning(f"Empty OTP provided for {phone_number}")
            return False, "❌ Please enter the OTP code."

        # Clean and validate OTP format
        otp = str(otp).strip()
        if not otp.isdigit():
            logger.warning(f"Non-numeric OTP provided for {phone_number}: {otp}")
            return (
                False,
                "❌ OTP must contain only numbers. Please check and try again.",
            )

        if len(otp) != self.otp_length:
            logger.warning(
                f"Wrong length OTP provided for {phone_number}: {otp} (expected {self.otp_length} digits)"
            )
            return (
                False,
                f"❌ OTP must be exactly {self.otp_length} digits. Please check and try again.",
            )

        key = self._get_otp_key(phone_number)
        redis_client = self._get_redis_client()

        stored_data = redis_client.hgetall(key)

        if not stored_data:
            logger.warning(f"No OTP found for {phone_number}")
            return False, "❌ OTP not found or expired. Please request a new code."

        # Convert bytes to strings if needed (handle both real Redis and mock Redis)
        stored_hash = ""
        created_at_str = ""

        # Try to get hash (handle both bytes and string keys)
        if b"hash" in stored_data:
            hash_value = stored_data[b"hash"]
            stored_hash = (
                hash_value.decode()
                if isinstance(hash_value, bytes)
                else str(hash_value)
            )
        elif "hash" in stored_data:
            hash_value = stored_data["hash"]
            stored_hash = (
                hash_value.decode()
                if isinstance(hash_value, bytes)
                else str(hash_value)
            )

        # Try to get created_at (handle both bytes and string keys)
        if b"created_at" in stored_data:
            time_value = stored_data[b"created_at"]
            created_at_str = (
                time_value.decode()
                if isinstance(time_value, bytes)
                else str(time_value)
            )
        elif "created_at" in stored_data:
            time_value = stored_data["created_at"]
            created_at_str = (
                time_value.decode()
                if isinstance(time_value, bytes)
                else str(time_value)
            )

        if not stored_hash:
            logger.warning(f"No OTP hash found for {phone_number}")
            return False, "❌ OTP expired. Please request a new code."

        # Check if OTP has expired
        if created_at_str:
            created_at = int(created_at_str)
            now = int(time.time())
            expiry_seconds = int(self.otp_expiry.total_seconds())
            time_diff = now - created_at

            if time_diff > expiry_seconds:
                logger.warning(f"OTP expired for {phone_number}")
                redis_client.delete(key)
                return False, "❌ OTP expired. Please request a new code."

        # Verify OTP with exact matching
        input_hash = hashlib.sha256(otp.encode()).hexdigest()

        if input_hash == stored_hash:
            # OTP verified successfully
            redis_client.delete(key)
            # Reset resend counter
            redis_client.delete(self._get_resend_key(phone_number))
            logger.info(f"OTP verified successfully for {phone_number}")
            return True, "🎉 OTP verified successfully! Welcome!"
        else:
            logger.warning(f"Invalid OTP provided for {phone_number}")
            return False, "❌ Incorrect OTP code. Please check the code and try again."

    def send_otp_sms(self, phone_number, otp):
        """Send OTP via SMS using Twilio or show in development mode."""
        # Check if Twilio is properly configured
        if not self.twilio_client or not self._is_twilio_configured():
            # Development mode - show OTP directly to user
            logger.info(f"DEVELOPMENT MODE: Showing OTP to user for {phone_number}")
            logger.info(f"OTP for {phone_number}: {otp}")
            print(f"\n✅ DEVELOPMENT MODE - OTP GENERATED ✅")
            print(f"📱 Phone: {phone_number}")
            print(f"🔐 OTP Code: {otp}")
            print(f"⏰ Valid for: {int(self.otp_expiry.total_seconds() / 60)} minutes")
            print(f"💡 Note: This is normal development behavior")
            print(f"{'='*45}\n")
            return (
                True,
                f"✅ Your OTP code is: {otp}",
            )

        try:
            message = self.twilio_client.messages.create(
                body=f"Your verification code is: {otp}. Valid for {int(self.otp_expiry.total_seconds() / 60)} minutes.",
                from_=self.twilio_phone,
                to=phone_number,
            )
            logger.info(f"SMS sent to {phone_number}, SID: {message.sid}")
            return True, "📱 OTP sent to your phone number"
        except TwilioRestException as e:
            logger.error(f"Failed to send SMS: {str(e)}")
            # Fallback to showing OTP directly when Twilio fails
            logger.warning(f"TWILIO ERROR - SHOWING OTP TO USER: {str(e)}")
            logger.warning(f"OTP for {phone_number}: {otp}")
            print(f"\n✅ DEVELOPMENT MODE (Twilio unavailable) ✅")
            print(f"📱 Phone: {phone_number}")
            print(f"🔐 OTP Code: {otp}")
            print(f"⏰ Valid for: {int(self.otp_expiry.total_seconds() / 60)} minutes")
            print(f"💡 Note: Configure Twilio for real SMS")
            print(f"{'='*50}\n")
            return (
                True,
                f"✅ Your OTP code is: {otp}",
            )

    def _is_twilio_configured(self):
        """Check if Twilio is properly configured with real credentials."""
        if not self.twilio_client:
            return False

        # Check if using placeholder values
        twilio_sid = os.environ.get("TWILIO_ACCOUNT_SID", "")
        twilio_token = os.environ.get("TWILIO_AUTH_TOKEN", "")
        twilio_phone = os.environ.get("TWILIO_PHONE_NUMBER", "")

        placeholder_values = [
            "your_twilio_account_sid",
            "your_twilio_auth_token",
            "your_twilio_phone_number",
        ]

        if any(
            cred in placeholder_values
            for cred in [twilio_sid, twilio_token, twilio_phone]
        ):
            return False

        return True

    def check_resend_limit(self, phone_number):
        """Check if user has exceeded resend limit."""
        key = self._get_resend_key(phone_number)
        redis_client = self._get_redis_client()
        count = redis_client.get(key)

        if count is None:
            return True

        count = int(count.decode()) if isinstance(count, bytes) else int(count)
        return count < self.resend_limit

    def increment_resend_count(self, phone_number):
        """Increment the resend counter for a phone number."""
        key = self._get_resend_key(phone_number)
        redis_client = self._get_redis_client()

        # Initialize or increment counter
        if not redis_client.exists(key):
            redis_client.set(key, 1)
            # Set expiry for 1 hour
            redis_client.expire(key, 3600)
        else:
            redis_client.incr(key)

        logger.info(f"Resend count incremented for {phone_number}")

    def log_otp_event(self, user, event_type, status, request=None):
        """Log OTP events for audit purposes."""
        ip_address = None
        user_agent = None

        if request:
            ip_address = request.remote_addr
            user_agent = request.user_agent.string if request.user_agent else None

        log_entry = OTPLog(
            user_id=user.id,
            event_type=event_type,
            status=status,
            ip_address=ip_address,
            user_agent=user_agent,
        )

        db.session.add(log_entry)
        db.session.commit()

        logger.info(f"OTP event logged: {event_type} {status} for user {user.id}")
