"""
Mock Redis implementation for development without a Redis server.
This is a simple in-memory implementation that mimics the Redis functions used in the application.
"""

import time
from datetime import datetime, timedelta


class MockRedis:
    """A simple in-memory mock of Redis for development purposes."""
    
    def __init__(self):
        """Initialize the mock Redis with an empty data store."""
        self.data = {}
        self.expiry = {}
    
    def from_url(self, url):
        """Mock the from_url method to return self."""
        return self
    
    def hset(self, key, field, value):
        """Set the value of a hash field."""
        if key not in self.data:
            self.data[key] = {}
        self.data[key][field] = value
        return 1
    
    def hget(self, key, field):
        """Get the value of a hash field."""
        if key not in self.data or field not in self.data[key]:
            return None
        self._check_expiry(key)
        return self.data[key].get(field)
    
    def hgetall(self, key):
        """Get all fields and values in a hash."""
        self._check_expiry(key)
        return self.data.get(key, {})
    
    def expire(self, key, seconds):
        """Set a key's time to live in seconds."""
        self.expiry[key] = datetime.now() + timedelta(seconds=seconds)
        return 1
    
    def delete(self, key):
        """Delete a key."""
        if key in self.data:
            del self.data[key]
        if key in self.expiry:
            del self.expiry[key]
        return 1
    
    def exists(self, key):
        """Check if a key exists."""
        self._check_expiry(key)
        return key in self.data
    
    def get(self, key):
        """Get the value of a key."""
        self._check_expiry(key)
        return self.data.get(key)
    
    def set(self, key, value, ex=None):
        """Set the value of a key with optional expiry."""
        self.data[key] = value
        if ex:
            self.expire(key, ex)
        return True
    
    def incr(self, key):
        """Increment the integer value of a key by one."""
        if key not in self.data:
            self.data[key] = 0
        self.data[key] = int(self.data[key]) + 1
        return self.data[key]
    
    def _check_expiry(self, key):
        """Check if a key has expired and delete it if so."""
        if key in self.expiry and datetime.now() > self.expiry[key]:
            self.delete(key)


# Create a singleton instance
mock_redis = MockRedis()
