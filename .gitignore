# Environment variables
.env
.env.*
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache
flask_session/

# Virtual Environment
venv/
ENV/
env/

# Database
*.db
*.sqlite3

# Logs
logs/
*.log

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
