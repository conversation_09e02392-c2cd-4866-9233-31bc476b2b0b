"""
Service for tracking and managing user login activities.
"""

import logging
from datetime import timed<PERSON><PERSON>
from flask import request
from app import db
from app.models.user import LoginActivity, get_ist_now, format_ist_datetime

logger = logging.getLogger(__name__)


class LoginActivityService:
    """Service for managing user login activity tracking."""

    @staticmethod
    def record_login(user, success=True):
        """
        Record a login activity for the user.

        Args:
            user: User object
            success: <PERSON><PERSON><PERSON> indicating if login was successful
        """
        try:
            # Extract request information
            ip_address = LoginActivityService._get_client_ip()
            user_agent = request.headers.get("User-Agent", "")

            # Create login activity record
            login_activity = LoginActivity(
                user_id=user.id,
                login_time=get_ist_now(),
                ip_address=ip_address,
                user_agent=user_agent,
                device_info=LoginActivityService._extract_device_info(user_agent),
                success=success,
            )

            db.session.add(login_activity)
            db.session.commit()

            logger.info(
                f"Login activity recorded for user {user.phone_number}: {success}"
            )

        except Exception as e:
            logger.error(
                f"Failed to record login activity for user {user.id}: {str(e)}"
            )
            db.session.rollback()

    @staticmethod
    def get_user_login_history(user, limit=50):
        """
        Get login history for a user.

        Args:
            user: User object
            limit: Maximum number of records to return

        Returns:
            List of LoginActivity objects
        """
        return user.login_activities.limit(limit).all()

    @staticmethod
    def get_recent_login_activities(user, limit=3):
        """
        Get recent login activities for a user (for profile page).

        Args:
            user: User object
            limit: Maximum number of records to return (default 3)

        Returns:
            List of LoginActivity objects
        """
        return user.login_activities.limit(limit).all()

    @staticmethod
    def get_total_login_count(user):
        """
        Get total count of login activities for a user.

        Args:
            user: User object

        Returns:
            Integer count of total login activities
        """
        return user.login_activities.count()

    @staticmethod
    def get_recent_logins(user, days=30):
        """
        Get recent login activities within specified days.

        Args:
            user: User object
            days: Number of days to look back

        Returns:
            List of LoginActivity objects
        """
        cutoff_date = get_ist_now() - timedelta(days=days)

        return user.login_activities.filter(
            LoginActivity.login_time >= cutoff_date
        ).all()

    @staticmethod
    def get_login_stats(user):
        """
        Get login statistics for a user.

        Args:
            user: User object

        Returns:
            Dictionary with login statistics
        """
        total_logins = user.login_activities.count()
        successful_logins = user.login_activities.filter(
            LoginActivity.success == True
        ).count()
        failed_logins = user.login_activities.filter(
            LoginActivity.success == False
        ).count()

        # Get last login
        last_login = user.login_activities.first()
        last_login_time = (
            format_ist_datetime(last_login.login_time) if last_login else None
        )

        # Get unique devices/browsers
        unique_devices = (
            db.session.query(LoginActivity.device_info)
            .filter(
                LoginActivity.user_id == user.id, LoginActivity.device_info.isnot(None)
            )
            .distinct()
            .count()
        )

        return {
            "total_logins": total_logins,
            "successful_logins": successful_logins,
            "failed_logins": failed_logins,
            "last_login": last_login_time,
            "unique_devices": unique_devices,
        }

    @staticmethod
    def _get_client_ip():
        """Extract client IP address from request."""
        # Check for forwarded IP first (in case of proxy/load balancer)
        if request.headers.get("X-Forwarded-For"):
            return request.headers.get("X-Forwarded-For").split(",")[0].strip()
        elif request.headers.get("X-Real-IP"):
            return request.headers.get("X-Real-IP")
        else:
            return request.remote_addr

    @staticmethod
    def _extract_device_info(user_agent):
        """Extract device information from user agent string."""
        if not user_agent:
            return "Unknown Device"

        user_agent = user_agent.lower()

        # Determine device type
        if "mobile" in user_agent or "android" in user_agent or "iphone" in user_agent:
            device_type = "Mobile"
        elif "tablet" in user_agent or "ipad" in user_agent:
            device_type = "Tablet"
        else:
            device_type = "Desktop"

        # Determine browser
        if "chrome" in user_agent:
            browser = "Chrome"
        elif "firefox" in user_agent:
            browser = "Firefox"
        elif "safari" in user_agent and "chrome" not in user_agent:
            browser = "Safari"
        elif "edge" in user_agent:
            browser = "Edge"
        elif "opera" in user_agent:
            browser = "Opera"
        else:
            browser = "Other"

        return f"{device_type} - {browser}"

    @staticmethod
    def cleanup_old_activities(days=90):
        """
        Clean up old login activities to prevent database bloat.

        Args:
            days: Number of days to keep activities
        """
        try:
            cutoff_date = get_ist_now() - timedelta(days=days)

            deleted_count = (
                db.session.query(LoginActivity)
                .filter(LoginActivity.login_time < cutoff_date)
                .delete()
            )

            db.session.commit()
            logger.info(f"Cleaned up {deleted_count} old login activities")

        except Exception as e:
            logger.error(f"Failed to cleanup old login activities: {str(e)}")
            db.session.rollback()
