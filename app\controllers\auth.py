import logging
from datetime import datetime, timedelta
from flask import (
    Blueprint,
    render_template,
    redirect,
    url_for,
    request,
    flash,
    current_app,
    session,
    jsonify,
)
from flask_login import login_user, logout_user, login_required, current_user
import phonenumbers

from app import db
from app.models.user import User
from app.services.otp_service import OTPService
from app.services.login_activity_service import LoginActivityService

# Initialize blueprint
auth_bp = Blueprint("auth", __name__, url_prefix="/auth")

# Initialize logger
logger = logging.getLogger(__name__)


@auth_bp.route("/register", methods=["GET", "POST"])
def register():
    """Handle user registration."""
    if current_user.is_authenticated:
        return redirect(url_for("main.dashboard"))

    if request.method == "POST":
        username = request.form.get("username", "").strip()
        display_name = request.form.get("display_name", "").strip()
        phone_number = request.form.get("phone_number")

        # Validate phone number format
        try:
            parsed_number = phonenumbers.parse(phone_number, None)
            if not phonenumbers.is_valid_number(parsed_number):
                flash(
                    "Invalid phone number format. Please enter a valid phone number.",
                    "danger",
                )
                return render_template("auth/register.html")

            # Format to E.164 standard
            phone_number = phonenumbers.format_number(
                parsed_number, phonenumbers.PhoneNumberFormat.E164
            )
        except phonenumbers.NumberParseException:
            flash(
                "Invalid phone number format. Please enter a valid phone number.",
                "danger",
            )
            return render_template("auth/register.html")

        # Check if phone number already exists
        existing_user = User.query.filter_by(phone_number=phone_number).first()
        if existing_user:
            flash("This phone number is already registered. Please login.", "warning")
            return redirect(url_for("auth.login"))

        # Create new user
        new_user = User(phone_number=phone_number)

        # Set username and display name
        try:
            if username:
                new_user.set_username(username)
            if display_name:
                new_user.display_name = display_name
        except ValueError as e:
            flash(str(e), "danger")
            return render_template("auth/register.html")

        db.session.add(new_user)
        db.session.commit()

        # Generate and send OTP
        return send_otp(new_user, "registration")

    return render_template("auth/register.html")


@auth_bp.route("/check-username", methods=["POST"])
def check_username():
    """Check if username is available."""
    data = request.get_json()
    username = data.get("username", "").strip().lower()

    if not username:
        return jsonify({"available": False, "message": "Username is required"})

    try:
        # Use the same validation as the model
        if len(username) < 3 or len(username) > 30:
            return jsonify(
                {
                    "available": False,
                    "message": "Username must be between 3 and 30 characters",
                }
            )

        if not username.replace("_", "").replace(".", "").isalnum():
            return jsonify(
                {
                    "available": False,
                    "message": "Username can only contain letters, numbers, underscores, and dots",
                }
            )

        if username.startswith(".") or username.endswith("."):
            return jsonify(
                {
                    "available": False,
                    "message": "Username cannot start or end with a dot",
                }
            )

        # Check if username exists
        existing_user = User.query.filter_by(username=username).first()
        available = existing_user is None

        return jsonify(
            {
                "available": available,
                "message": (
                    "Username is available!"
                    if available
                    else "Username is already taken"
                ),
            }
        )
    except Exception as e:
        return jsonify({"available": False, "message": "Error checking username"})


@auth_bp.route("/login", methods=["GET", "POST"])
def login():
    """Handle user login."""
    if current_user.is_authenticated:
        return redirect(url_for("main.dashboard"))

    if request.method == "POST":
        identifier = request.form.get("identifier", "").strip()

        # Try to find user by username first, then by phone number
        user = User.find_by_username_or_phone(identifier)

        # If not found by username, try to parse as phone number
        if not user and identifier.startswith("+"):
            try:
                parsed_number = phonenumbers.parse(identifier, None)
                if phonenumbers.is_valid_number(parsed_number):
                    # Format to E.164 standard
                    phone_number = phonenumbers.format_number(
                        parsed_number, phonenumbers.PhoneNumberFormat.E164
                    )
                    user = User.query.filter_by(phone_number=phone_number).first()
            except phonenumbers.NumberParseException:
                pass

        if not user:
            flash(
                "Username or phone number not found. Please check your input or register first.",
                "warning",
            )
            return redirect(url_for("auth.register"))

        # Check if user is locked out due to too many OTP attempts (only if lockout is enabled)
        if (
            current_app.config.get("OTP_LOCKOUT_ENABLED", True)
            and user.otp_locked_until
            and user.otp_locked_until > datetime.utcnow()
        ):
            remaining_time = (
                user.otp_locked_until - datetime.utcnow()
            ).total_seconds() / 60
            flash(
                f"Account temporarily locked due to too many failed attempts. Try again in {int(remaining_time)} minutes.",
                "danger",
            )
            return render_template("auth/login.html")

        # Generate and send OTP
        return send_otp(user, "login")

    return render_template("auth/login.html")


@auth_bp.route("/verify-otp", methods=["GET", "POST"])
def verify_otp():
    """Handle OTP verification."""
    # Get user_id from session
    user_id = session.get("otp_user_id")
    if not user_id:
        flash("Session expired. Please try again.", "warning")
        return redirect(url_for("auth.login"))

    user = User.query.get(user_id)
    if not user:
        flash("User not found. Please register.", "warning")
        return redirect(url_for("auth.register"))

    # Check if user is locked out due to too many OTP attempts (only if lockout is enabled)
    if (
        current_app.config.get("OTP_LOCKOUT_ENABLED", True)
        and user.otp_locked_until
        and user.otp_locked_until > datetime.utcnow()
    ):
        remaining_time = (
            user.otp_locked_until - datetime.utcnow()
        ).total_seconds() / 60
        flash(
            f"Account temporarily locked due to too many failed attempts. Try again in {int(remaining_time)} minutes.",
            "danger",
        )
        return redirect(url_for("auth.login"))

    if request.method == "POST":
        otp_code = request.form.get("otp", "").strip()

        # Initialize OTP service
        otp_service = OTPService(current_app.config)

        # Log the verification attempt for debugging
        logger.info(
            f"OTP verification attempt for {user.phone_number}: '{otp_code}' (length: {len(otp_code)})"
        )

        # Verify OTP
        is_valid, message = otp_service.verify_otp(user.phone_number, otp_code)

        # Log the verification attempt
        otp_service.log_otp_event(
            user=user,
            event_type="verify",
            status="success" if is_valid else "failure",
            request=request,
        )

        if is_valid:
            # Reset OTP attempts
            user.reset_otp_attempts()

            # Mark user as verified if this was a registration flow
            if session.get("otp_flow") == "registration":
                user.is_verified = True
                db.session.commit()

            # Log the user in
            login_user(user)

            # Record successful login activity
            LoginActivityService.record_login(user, success=True)

            # Clear OTP session data
            session.pop("otp_user_id", None)
            session.pop("otp_flow", None)

            flash(message, "success")
            return redirect(url_for("main.dashboard"))
        else:
            # Increment failed attempts
            user.increment_otp_attempts()

            # Check if max attempts reached and lockout is enabled
            if user.otp_attempts >= current_app.config.get(
                "OTP_MAX_ATTEMPTS", 3
            ) and current_app.config.get("OTP_LOCKOUT_ENABLED", True):
                # Lock account for configured duration
                lockout_duration = current_app.config.get(
                    "OTP_LOCKOUT_DURATION", timedelta(minutes=30)
                )
                user.otp_locked_until = datetime.utcnow() + lockout_duration
                db.session.commit()

                lockout_minutes = int(lockout_duration.total_seconds() / 60)
                flash(
                    f"Maximum OTP attempts exceeded. Account locked for {lockout_minutes} minutes.",
                    "danger",
                )
                return redirect(url_for("auth.login"))

            flash(message, "danger")

    return render_template("auth/verify_otp.html", phone_number=user.phone_number)


@auth_bp.route("/resend-otp", methods=["POST"])
def resend_otp():
    """Resend OTP to user."""
    # Get user_id from session
    user_id = session.get("otp_user_id")
    if not user_id:
        flash("Session expired. Please try again.", "warning")
        return redirect(url_for("auth.login"))

    user = User.query.get(user_id)
    if not user:
        flash("User not found. Please register.", "warning")
        return redirect(url_for("auth.register"))

    # Check if user is locked out due to too many OTP attempts (only if lockout is enabled)
    if (
        current_app.config.get("OTP_LOCKOUT_ENABLED", True)
        and user.otp_locked_until
        and user.otp_locked_until > datetime.utcnow()
    ):
        remaining_time = (
            user.otp_locked_until - datetime.utcnow()
        ).total_seconds() / 60
        flash(
            f"Account temporarily locked due to too many failed attempts. Try again in {int(remaining_time)} minutes.",
            "danger",
        )
        return redirect(url_for("auth.login"))

    # Get flow type from session
    flow = session.get("otp_flow", "login")

    # Send OTP
    return send_otp(user, flow, is_resend=True)


@auth_bp.route("/logout")
@login_required
def logout():
    """Handle user logout."""
    logout_user()
    flash("You have been logged out.", "info")
    return redirect(url_for("auth.login"))


@auth_bp.route("/unlock-account", methods=["GET", "POST"])
def unlock_account():
    """Development route to unlock locked accounts."""
    # Only allow in development mode
    if not current_app.config.get("DEBUG", False):
        flash("This feature is only available in development mode.", "danger")
        return redirect(url_for("auth.login"))

    if request.method == "POST":
        phone_number = request.form.get("phone_number")
        if phone_number:
            user = User.query.filter_by(phone_number=phone_number).first()
            if user:
                user.reset_otp_attempts()
                flash(
                    f"Account {phone_number} has been unlocked and OTP attempts reset.",
                    "success",
                )
            else:
                flash("Phone number not found.", "warning")
        else:
            flash("Please enter a phone number.", "warning")

    return render_template("auth/unlock_account.html")


@auth_bp.route("/dev-otp/<phone_number>")
def dev_otp(phone_number):
    """Development route to show current OTP for a phone number."""
    # Only allow in development mode
    if not current_app.config.get("DEBUG", False):
        return "This feature is only available in development mode.", 403

    from app.services.otp_service import OTPService

    otp_service = OTPService(current_app.config)
    redis_client = otp_service._get_redis_client()

    key = f"otp:{phone_number}"
    stored_data = redis_client.hgetall(key)

    if not stored_data:
        return f"No OTP found for {phone_number}", 404

    # This is a simple development helper - return plain text
    return f"Current OTP for {phone_number}: Check console output", 200


def send_otp(user, flow, is_resend=False):
    """Helper function to generate and send OTP."""
    # Initialize OTP service
    otp_service = OTPService(current_app.config)

    # Check resend limit if this is a resend
    if is_resend and not otp_service.check_resend_limit(user.phone_number):
        flash(
            "You have exceeded the OTP resend limit. Please try again later.", "warning"
        )
        return redirect(url_for("auth.login"))

    # Generate OTP
    otp = otp_service.generate_otp()

    # Store OTP
    otp_service.store_otp(user.phone_number, otp)

    # Send OTP via SMS
    success, message = otp_service.send_otp_sms(user.phone_number, otp)

    # Log the OTP event
    event_type = "resend" if is_resend else "generate"
    otp_service.log_otp_event(
        user=user,
        event_type=event_type,
        status="success" if success else "failure",
        request=request,
    )

    if success:
        # Store user_id and flow in session
        session["otp_user_id"] = user.id
        session["otp_flow"] = flow

        # Increment resend count if this is a resend
        if is_resend:
            otp_service.increment_resend_count(user.phone_number)

        # Show the message from OTP service (includes OTP in development mode)
        flash(message, "success")
        return redirect(url_for("auth.verify_otp"))
    else:
        flash(f"Failed to send OTP: {message}", "danger")
        return redirect(url_for("auth.login"))
