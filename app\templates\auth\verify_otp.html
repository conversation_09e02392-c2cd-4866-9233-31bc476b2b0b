{% extends 'base.html' %}

{% block title %}Verify OTP - OTP Authentication{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h4 class="mb-0"><i class="fas fa-key me-2"></i>Verify OTP</h4>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <div class="alert alert-info border-0"
                        style="background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); color: white;">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <span class="toast-emoji me-2">🔐</span>
                            <strong>OTP Verification</strong>
                        </div>
                        <p class="mb-2">Enter the 6-digit verification code sent to:</p>
                        <strong class="fs-5">{{ phone_number }}</strong>
                    </div>

                    <div class="d-flex align-items-center justify-content-center text-muted small">
                        <i class="fas fa-clock me-2"></i>
                        <span>Code expires in </span>
                        <span id="otp-timer" class="fw-bold text-warning ms-1">5:00</span>
                    </div>

                    <!-- Progress bar for time remaining -->
                    <div class="progress-modern mt-2">
                        <div class="progress-bar" id="time-progress" style="width: 100%"></div>
                    </div>
                </div>

                <form method="POST" action="{{ url_for('auth.verify_otp') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-4">
                        <label for="otp" class="form-label text-center d-block">
                            <span class="fw-bold">Enter Verification Code</span>
                            <span class="d-block small text-muted mt-1">Tap each box to enter your code</span>
                        </label>
                        <div class="otp-input-container d-flex justify-content-center">
                            <!-- Modern OTP inputs will be created here by JavaScript -->
                            <input type="hidden" id="otp" name="otp" required>
                        </div>
                        <div class="form-text text-center mt-3">
                            <span class="text-muted">💡 Tip: You can paste the entire code at once!</span>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Verify</button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <div class="border-top pt-4">
                        <div class="mb-3">
                            <span class="text-muted">Didn't receive the code? 🤔</span>
                        </div>
                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                            <form method="POST" action="{{ url_for('auth.resend_otp') }}" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-outline-primary btn-sm pulse-animation">
                                    <i class="fas fa-paper-plane me-1"></i>Resend Code
                                </button>
                            </form>
                            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>Back to Login
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Check your spam folder or try resending
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Enhanced timer with progress bar
        let timeLeft = 300; // 5 minutes in seconds
        const timerElement = document.getElementById('otp-timer');
        const progressElement = document.getElementById('time-progress');
        const totalTime = 300;

        if (timerElement && progressElement) {
            const countdownTimer = setInterval(function () {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;

                timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                // Update progress bar
                const progressPercent = (timeLeft / totalTime) * 100;
                progressElement.style.width = progressPercent + '%';

                // Change colors based on time remaining
                if (timeLeft <= 60) {
                    timerElement.classList.add('text-danger');
                    progressElement.style.background = 'linear-gradient(90deg, #ff416c, #ff4b2b)';
                } else if (timeLeft <= 120) {
                    timerElement.classList.add('text-warning');
                    progressElement.style.background = 'linear-gradient(90deg, #f093fb, #f5576c)';
                }

                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    timerElement.textContent = 'Expired';
                    timerElement.classList.add('text-danger');
                    progressElement.style.width = '0%';

                    // Show expiration notification
                    if (typeof notifications !== 'undefined') {
                        notifications.error('Your OTP has expired! Please request a new one. ⏰', {
                            title: '⚠️ Time\'s Up!',
                            duration: 8000
                        });
                    }

                    // Disable the form
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = '<i class="fas fa-clock me-1"></i>Expired';
                        submitButton.classList.add('btn-danger');
                        submitButton.classList.remove('btn-primary');
                    }
                }

                timeLeft--;
            }, 1000);
        }

        // Add success notification when OTP is successfully submitted
        const form = document.querySelector('form[action*="verify-otp"]');
        if (form) {
            form.addEventListener('submit', function (e) {
                const otpInputs = document.querySelectorAll('.otp-digit');
                const hiddenOtp = document.getElementById('otp');

                if (otpInputs.length > 0) {
                    // Get OTP from individual inputs
                    const otp = Array.from(otpInputs).map(input => input.value).join('');
                    if (otp.length === 6) {
                        hiddenOtp.value = otp;

                        // Show verification notification
                        if (typeof notifications !== 'undefined') {
                            notifications.info('Verifying your code... 🔍', {
                                title: '🔐 Checking...',
                                duration: 3000
                            });
                        }
                    } else {
                        e.preventDefault();
                        if (typeof notifications !== 'undefined') {
                            notifications.warning('Please enter all 6 digits! 🔢', {
                                title: '⚠️ Incomplete',
                                duration: 3000
                            });
                        }
                    }
                }
            });
        }

        // Add notification for resend button
        const resendForm = document.querySelector('form[action*="resend-otp"]');
        if (resendForm) {
            resendForm.addEventListener('submit', function (e) {
                if (typeof notifications !== 'undefined') {
                    notifications.info('Sending a new code to your phone... 📱', {
                        title: '📤 Resending...',
                        duration: 4000
                    });
                }
            });
        }
    });
</script>
{% endblock %}