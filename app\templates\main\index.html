{% extends 'base.html' %}

{% block title %}🚀 Premium Authentication Experience{% endblock %}

{% block content %}
<!-- 🌟 Premium Hero Section -->
<div class="hero-section py-5 mb-5">
    <div class="row align-items-center min-vh-75">
        <div class="col-lg-6 order-lg-2">
            <div class="hero-image-container">
                <img src="{{ url_for('static', filename='images/security-hero.svg') }}" alt="Premium Security"
                    class="img-fluid hero-image">
                <div class="floating-elements">
                    <div class="floating-icon" style="top: 10%; left: 10%;">🔐</div>
                    <div class="floating-icon" style="top: 20%; right: 15%;">✨</div>
                    <div class="floating-icon" style="bottom: 30%; left: 20%;">🚀</div>
                    <div class="floating-icon" style="bottom: 10%; right: 10%;">🛡️</div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 order-lg-1 py-4">
            <div class="hero-content">
                <div class="badge-premium mb-4">
                    <span class="badge bg-gradient-primary px-3 py-2">
                        <i class="fas fa-crown me-2"></i>Premium Experience
                    </span>
                </div>
                <h1 class="display-3 fw-bold mb-4">
                    <span style="color: #2d3748; font-weight: 700;">The Future of</span><br>
                    <span style="background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 800;">Authentication</span>
                </h1>
                <p class="lead mb-4" style="color: #64748b;">
                    Experience next-generation security with our premium OTP verification system.
                    Beautiful, secure, and lightning-fast.
                </p>
                <div class="hero-stats mb-4">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item"
                                style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">
                                <h3 class="mb-0" style="color: #4361ee;">99.9%</h3>
                                <small style="color: #64748b;">Uptime</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item"
                                style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">
                                <h3 class="mb-0" style="color: #4361ee;">&lt;2s</h3>
                                <small style="color: #64748b;">Delivery</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item"
                                style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem;">
                                <h3 class="mb-0" style="color: #4361ee;">256-bit</h3>
                                <small style="color: #64748b;">Encryption</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="premium-features mb-5">
                    <div class="feature-item mb-3"
                        style="display: flex; align-items: center; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem; transition: all 0.3s ease;">
                        <div class="feature-icon me-3"
                            style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); border-radius: 50%; color: white; font-size: 1.25rem; flex-shrink: 0;">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <h6 class="mb-1" style="color: #1e293b;">🛡️ Military-Grade Security</h6>
                            <p class="mb-0 small" style="color: #64748b;">Advanced encryption with multi-layer
                                protection</p>
                        </div>
                    </div>

                    <div class="feature-item mb-3"
                        style="display: flex; align-items: center; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem; transition: all 0.3s ease;">
                        <div class="feature-icon me-3"
                            style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); border-radius: 50%; color: white; font-size: 1.25rem; flex-shrink: 0;">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div>
                            <h6 class="mb-1" style="color: #1e293b;">⚡ Lightning Fast</h6>
                            <p class="mb-0 small" style="color: #64748b;">Instant OTP delivery in under 2 seconds</p>
                        </div>
                    </div>

                    <div class="feature-item"
                        style="display: flex; align-items: center; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem; transition: all 0.3s ease;">
                        <div class="feature-icon me-3"
                            style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); border-radius: 50%; color: white; font-size: 1.25rem; flex-shrink: 0;">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div>
                            <h6 class="mb-1" style="color: #1e293b;">✨ Premium Experience</h6>
                            <p class="mb-0 small" style="color: #64748b;">Beautiful interface with smooth animations</p>
                        </div>
                    </div>
                </div>

                <div class="hero-actions">
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary btn-lg px-5 py-3 me-3">
                        <i class="fas fa-tachometer-alt me-2"></i>Enter Dashboard
                    </a>
                    {% else %}
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg px-5 py-3 me-3">
                        <i class="fas fa-rocket me-2"></i>Start Your Journey
                    </a>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-lg px-4 py-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>
</div>

<!-- 🎯 Premium How It Works Section -->
<div class="premium-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-3" style="color: #1e293b;">How It Works</h2>
            <p class="lead" style="color: #64748b;">Three simple steps to premium security</p>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="premium-step-card text-center"
                    style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 3rem 2rem; position: relative; transition: all 0.3s ease; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="step-number"
                        style="position: absolute; top: -20px; right: 20px; background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 1.25rem; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
                        01</div>
                    <div class="step-icon mb-4"
                        style="width: 80px; height: 80px; background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 2rem; color: #4361ee; transition: all 0.3s ease;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h4 class="mb-3" style="color: #1e293b;">🚀 Register</h4>
                    <p style="color: #64748b;">Create your premium account with just your phone number. Quick, secure,
                        and
                        hassle-free.</p>
                    <div class="step-connector"></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="premium-step-card text-center"
                    style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 3rem 2rem; position: relative; transition: all 0.3s ease; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="step-number"
                        style="position: absolute; top: -20px; right: 20px; background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 1.25rem; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
                        02</div>
                    <div class="step-icon mb-4"
                        style="width: 80px; height: 80px; background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 2rem; color: #4361ee; transition: all 0.3s ease;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 class="mb-3" style="color: #1e293b;">⚡ Receive OTP</h4>
                    <p style="color: #64748b;">Get your 6-digit verification code instantly. Lightning-fast delivery in
                        under 2 seconds.</p>
                    <div class="step-connector"></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="premium-step-card text-center"
                    style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 3rem 2rem; position: relative; transition: all 0.3s ease; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="step-number"
                        style="position: absolute; top: -20px; right: 20px; background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 1.25rem; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
                        03</div>
                    <div class="step-icon mb-4"
                        style="width: 80px; height: 80px; background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 2rem; color: #4361ee; transition: all 0.3s ease;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="mb-3" style="color: #1e293b;">✨ Verify & Access</h4>
                    <p style="color: #64748b;">Enter your code and enjoy premium access. Military-grade security meets
                        beautiful design.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* 🌟 Premium Homepage Styles */
    .hero-section {
        position: relative;
        overflow: hidden;
        background-color: #f8fafc;
    }

    .min-vh-75 {
        min-height: 75vh;
    }

    .hero-image-container {
        position: relative;
    }

    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .floating-icon {
        position: absolute;
        font-size: 2rem;
        animation: float-gentle 6s ease-in-out infinite;
        opacity: 0.8;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    .floating-icon:nth-child(1) {
        animation-delay: 0s;
    }

    .floating-icon:nth-child(2) {
        animation-delay: 1.5s;
    }

    .floating-icon:nth-child(3) {
        animation-delay: 3s;
    }

    .floating-icon:nth-child(4) {
        animation-delay: 4.5s;
    }

    .badge-premium .badge {
        background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-size: 0.9rem;
        animation: gentle-glow 4s ease-in-out infinite;
        transition: all 0.3s ease;
    }

    .badge-premium .badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
    }

    @keyframes gentle-glow {

        0%,
        100% {
            box-shadow: 0 0 10px rgba(67, 97, 238, 0.3);
        }

        50% {
            box-shadow: 0 0 20px rgba(76, 201, 240, 0.4);
        }
    }

    .hero-actions .btn {
        position: relative;
        overflow: hidden;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .premium-section {
        background: #f8fafc;
        border-top: 1px solid #e2e8f0;
    }

    .step-connector {
        position: absolute;
        top: 50%;
        right: -50px;
        width: 100px;
        height: 2px;
        background: linear-gradient(90deg, #e2e8f0, transparent);
        transform: translateY(-50%);
    }

    .step-connector::after {
        content: '→';
        position: absolute;
        right: -20px;
        top: -10px;
        color: #64748b;
        font-size: 1.5rem;
    }

    @media (max-width: 768px) {
        .step-connector {
            display: none;
        }

        .floating-icon {
            font-size: 1.5rem;
        }
    }

    /* Hover effects */
    .premium-step-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .premium-step-card:hover .step-icon {
        background: linear-gradient(135deg, #4361ee 0%, #4cc9f0 100%) !important;
        color: white !important;
        transform: scale(1.1) rotate(5deg);
    }

    .feature-item:hover {
        transform: translateX(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
</style>
{% endblock %}