{% extends 'base.html' %}

{% block title %}🚀 Premium Authentication Experience{% endblock %}

{% block content %}
<!-- 🌟 Premium Hero Section -->
<div class="hero-section py-5 mb-5">
    <div class="row align-items-center min-vh-75">
        <div class="col-lg-6 order-lg-2">
            <div class="hero-image-container">
                <img src="{{ url_for('static', filename='images/security-hero.svg') }}" alt="Premium Security"
                    class="img-fluid hero-image">
                <div class="floating-elements">
                    <div class="floating-icon" style="top: 10%; left: 10%;">🔐</div>
                    <div class="floating-icon" style="top: 20%; right: 15%;">✨</div>
                    <div class="floating-icon" style="bottom: 30%; left: 20%;">🚀</div>
                    <div class="floating-icon" style="bottom: 10%; right: 10%;">🛡️</div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 order-lg-1 py-4">
            <div class="hero-content">
                <div class="badge-premium mb-4">
                    <span class="badge bg-gradient-primary px-3 py-2">
                        <i class="fas fa-crown me-2"></i>Premium Experience
                    </span>
                </div>
                <h1 class="display-3 fw-bold text-gradient mb-4">
                    The Future of<br>
                    <span class="text-shadow">Authentication</span>
                </h1>
                <p class="lead text-white-75 mb-4">
                    Experience next-generation security with our premium OTP verification system.
                    Beautiful, secure, and lightning-fast.
                </p>
                <div class="hero-stats mb-4">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="text-gradient mb-0">99.9%</h3>
                                <small class="text-white-50">Uptime</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="text-gradient mb-0">&lt;2s</h3>
                                <small class="text-white-50">Delivery</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="text-gradient mb-0">256-bit</h3>
                                <small class="text-white-50">Encryption</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="premium-features mb-5">
                    <div class="feature-item mb-3">
                        <div class="feature-icon me-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">🛡️ Military-Grade Security</h6>
                            <p class="text-white-50 mb-0 small">Advanced encryption with multi-layer protection</p>
                        </div>
                    </div>

                    <div class="feature-item mb-3">
                        <div class="feature-icon me-3">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">⚡ Lightning Fast</h6>
                            <p class="text-white-50 mb-0 small">Instant OTP delivery in under 2 seconds</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon me-3">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-1">✨ Premium Experience</h6>
                            <p class="text-white-50 mb-0 small">Beautiful interface with smooth animations</p>
                        </div>
                    </div>
                </div>

                <div class="hero-actions">
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary btn-lg px-5 py-3 me-3">
                        <i class="fas fa-tachometer-alt me-2"></i>Enter Dashboard
                    </a>
                    {% else %}
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg px-5 py-3 me-3">
                        <i class="fas fa-rocket me-2"></i>Start Your Journey
                    </a>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-lg px-4 py-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>
</div>

<!-- 🎯 Premium How It Works Section -->
<div class="premium-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-gradient mb-3">How It Works</h2>
            <p class="lead text-white-75">Three simple steps to premium security</p>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="premium-step-card text-center">
                    <div class="step-number">01</div>
                    <div class="step-icon mb-4">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h4 class="text-white mb-3">🚀 Register</h4>
                    <p class="text-white-75">Create your premium account with just your phone number. Quick, secure, and
                        hassle-free.</p>
                    <div class="step-connector"></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="premium-step-card text-center">
                    <div class="step-number">02</div>
                    <div class="step-icon mb-4">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 class="text-white mb-3">⚡ Receive OTP</h4>
                    <p class="text-white-75">Get your 6-digit verification code instantly. Lightning-fast delivery in
                        under 2 seconds.</p>
                    <div class="step-connector"></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="premium-step-card text-center">
                    <div class="step-number">03</div>
                    <div class="step-icon mb-4">
                        <i class="fas fa-shield-check"></i>
                    </div>
                    <h4 class="text-white mb-3">✨ Verify & Access</h4>
                    <p class="text-white-75">Enter your code and enjoy premium access. Military-grade security meets
                        beautiful design.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* 🌟 Premium Homepage Styles */
    .hero-section {
        position: relative;
        overflow: hidden;
    }

    .min-vh-75 {
        min-height: 75vh;
    }

    .hero-image-container {
        position: relative;
    }

    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .floating-icon {
        position: absolute;
        font-size: 2rem;
        animation: float-gentle 6s ease-in-out infinite;
        opacity: 0.8;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }

    .floating-icon:nth-child(1) {
        animation-delay: 0s;
    }

    .floating-icon:nth-child(2) {
        animation-delay: 1.5s;
    }

    .floating-icon:nth-child(3) {
        animation-delay: 3s;
    }

    .floating-icon:nth-child(4) {
        animation-delay: 4.5s;
    }

    .badge-premium .badge {
        background: var(--primary-gradient) !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-size: 0.9rem;
        animation: pulse 3s infinite;
    }

    .text-white-75 {
        color: rgba(255, 255, 255, 0.75) !important;
    }

    .text-white-50 {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    .hero-stats .stat-item {
        padding: 1rem;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        transition: all var(--transition-smooth);
    }

    .hero-stats .stat-item:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
    }

    .premium-features .feature-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        transition: all var(--transition-smooth);
    }

    .premium-features .feature-item:hover {
        transform: translateX(10px);
        background: rgba(255, 255, 255, 0.15);
    }

    .premium-features .feature-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-gradient);
        border-radius: 50%;
        color: white;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .hero-actions .btn {
        position: relative;
        overflow: hidden;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .premium-section {
        background: rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .premium-step-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-lg);
        padding: 3rem 2rem;
        position: relative;
        transition: all var(--transition-smooth);
        overflow: hidden;
    }

    .premium-step-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--primary-gradient);
    }

    .premium-step-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow: var(--shadow-strong);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .step-number {
        position: absolute;
        top: -20px;
        right: 20px;
        background: var(--primary-gradient);
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.25rem;
        box-shadow: var(--shadow-medium);
    }

    .step-icon {
        width: 80px;
        height: 80px;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 2px solid var(--glass-border);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: 2rem;
        color: white;
        transition: all var(--transition-smooth);
    }

    .premium-step-card:hover .step-icon {
        background: var(--primary-gradient);
        border-color: rgba(255, 255, 255, 0.6);
        transform: scale(1.1) rotate(5deg);
    }

    .step-connector {
        position: absolute;
        top: 50%;
        right: -50px;
        width: 100px;
        height: 2px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
        transform: translateY(-50%);
    }

    .step-connector::after {
        content: '→';
        position: absolute;
        right: -20px;
        top: -10px;
        color: rgba(255, 255, 255, 0.5);
        font-size: 1.5rem;
    }

    @media (max-width: 768px) {
        .step-connector {
            display: none;
        }

        .floating-icon {
            font-size: 1.5rem;
        }

        .premium-step-card {
            padding: 2rem 1.5rem;
        }

        .step-number {
            width: 50px;
            height: 50px;
            font-size: 1rem;
        }
    }
</style>
{% endblock %}