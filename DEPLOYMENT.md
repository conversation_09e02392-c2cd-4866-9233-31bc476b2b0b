# Production Deployment Guide

## Prerequisites

- Python 3.8+
- PostgreSQL or MySQL database
- Redis server
- Twilio account
- Web server (Nginx/Apache)
- SSL certificate

## Environment Setup

1. **Create production environment file:**
```bash
cp .env.example .env
```

2. **Configure production variables:**
```bash
FLASK_ENV=production
SECRET_KEY=your_very_secure_secret_key_32_chars_min
DATABASE_URL=postgresql://user:pass@localhost:5432/otpauth
REDIS_URL=redis://localhost:6379/0
TWILIO_ACCOUNT_SID=your_real_twilio_sid
TWILIO_AUTH_TOKEN=your_real_twilio_token
TWILIO_PHONE_NUMBER=your_verified_twilio_number
```

## Database Setup

```bash
# Install PostgreSQL driver
pip install psycopg2-binary

# Initialize database
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

## Security Checklist

- [ ] Strong SECRET_KEY (32+ characters)
- [ ] HTTPS enabled
- [ ] Database credentials secured
- [ ] Redis password protected
- [ ] Firewall configured
- [ ] Regular backups scheduled
- [ ] Monitoring setup

## Nginx Configuration

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /path/to/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Systemd Service

```ini
[Unit]
Description=OTP Auth App
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/app
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:5000 run:app
Restart=always

[Install]
WantedBy=multi-user.target
```

## Monitoring

- Set up log rotation
- Monitor Redis memory usage
- Database performance monitoring
- SSL certificate expiration alerts
- Uptime monitoring
