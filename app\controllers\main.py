from flask import Blueprint, render_template, request, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.services.login_activity_service import LoginActivityService

# Initialize blueprint
main_bp = Blueprint("main", __name__)


@main_bp.route("/")
def index():
    """Render the home page."""
    return render_template("main/index.html")


@main_bp.route("/dashboard")
@login_required
def dashboard():
    """Render the dashboard page for authenticated users."""
    return render_template("main/dashboard.html")


@main_bp.route("/profile")
@login_required
def profile():
    """Render the user profile page."""
    # Get recent login activities for the profile page (last 3)
    recent_activities = LoginActivityService.get_recent_login_activities(
        current_user, limit=3
    )
    total_activities = LoginActivityService.get_total_login_count(current_user)

    return render_template(
        "main/profile.html",
        recent_activities=recent_activities,
        total_activities=total_activities,
    )


@main_bp.route("/update-profile", methods=["POST"])
@login_required
def update_profile():
    """Update user profile information."""
    username = request.form.get("username", "").strip()
    display_name = request.form.get("display_name", "").strip()

    try:
        # Update username if provided
        if username:
            current_user.set_username(username)
        else:
            current_user.username = None

        # Update display name
        current_user.display_name = display_name if display_name else None

        db.session.commit()
        flash("Profile updated successfully!", "success")

    except ValueError as e:
        flash(str(e), "danger")
    except Exception as e:
        flash(
            "An error occurred while updating your profile. Please try again.", "danger"
        )
        db.session.rollback()

    return redirect(url_for("main.profile"))


@main_bp.route("/login-activity")
@login_required
def login_activity():
    """Render the login activity page."""
    # Get login history and stats
    login_history = LoginActivityService.get_user_login_history(current_user, limit=50)
    login_stats = LoginActivityService.get_login_stats(current_user)
    recent_logins = LoginActivityService.get_recent_logins(current_user, days=30)

    return render_template(
        "main/login_activity.html",
        login_history=login_history,
        login_stats=login_stats,
        recent_logins=recent_logins,
    )


@main_bp.route("/about")
def about():
    """Render the about page."""
    return render_template("main/about.html")
