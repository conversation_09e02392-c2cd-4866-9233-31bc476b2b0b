<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4e73df;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#1cc88a;stop-opacity:0.1" />
    </linearGradient>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4e73df;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e59d9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bgGradient)"/>
  
  <!-- Main Shield -->
  <g transform="translate(400,300)">
    <!-- Shield Background -->
    <path d="M-80,-120 L80,-120 L80,60 L0,120 L-80,60 Z" fill="url(#shieldGradient)" stroke="#2653d4" stroke-width="3"/>
    
    <!-- Lock Icon -->
    <g transform="translate(0,-20)">
      <!-- Lock Body -->
      <rect x="-25" y="0" width="50" height="40" rx="5" fill="white"/>
      <!-- Lock Shackle -->
      <path d="M-15,-20 Q-15,-35 0,-35 Q15,-35 15,-20" stroke="white" stroke-width="6" fill="none"/>
      <!-- Keyhole -->
      <circle cx="0" cy="15" r="6" fill="#4e73df"/>
      <rect x="-2" y="18" width="4" height="8" fill="#4e73df"/>
    </g>
    
    <!-- Decorative Elements -->
    <circle cx="-50" cy="-80" r="3" fill="white" opacity="0.7"/>
    <circle cx="45" cy="-70" r="2" fill="white" opacity="0.5"/>
    <circle cx="-40" cy="30" r="2" fill="white" opacity="0.6"/>
    <circle cx="50" cy="20" r="3" fill="white" opacity="0.4"/>
  </g>
  
  <!-- Floating Security Elements -->
  <g opacity="0.3">
    <!-- Phone with OTP -->
    <g transform="translate(150,450)">
      <rect x="0" y="0" width="60" height="100" rx="10" fill="#4e73df"/>
      <rect x="5" y="15" width="50" height="70" rx="3" fill="white"/>
      <text x="30" y="40" text-anchor="middle" font-family="Arial" font-size="12" fill="#4e73df">OTP</text>
      <text x="30" y="55" text-anchor="middle" font-family="Arial" font-size="10" fill="#4e73df">123456</text>
    </g>
    
    <!-- Key -->
    <g transform="translate(650,150)">
      <circle cx="0" cy="0" r="15" fill="#1cc88a"/>
      <rect x="15" y="-3" width="40" height="6" fill="#1cc88a"/>
      <rect x="50" y="-8" width="5" height="6" fill="#1cc88a"/>
      <rect x="50" y="2" width="5" height="6" fill="#1cc88a"/>
    </g>
    
    <!-- Checkmark -->
    <g transform="translate(100,150)">
      <circle cx="0" cy="0" r="20" fill="#1cc88a"/>
      <path d="M-8,0 L-3,8 L12,-8" stroke="white" stroke-width="4" fill="none" stroke-linecap="round"/>
    </g>
  </g>
  
  <!-- Network Lines -->
  <g stroke="#4e73df" stroke-width="2" opacity="0.2">
    <line x1="150" y1="450" x2="320" y2="300"/>
    <line x1="650" y1="150" x2="480" y2="300"/>
    <line x1="100" y1="150" x2="320" y2="300"/>
  </g>
</svg>
